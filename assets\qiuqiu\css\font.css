.options {
	width: 100%;
	height: 155px;
	position: relative;
}

ul.op {
	width: 65px;
	height: 100%;
	position: absolute;
	right: 5px;
}

ul.op li {
	height: 45px;
	margin-top: 5px;
	line-height: 48px;
	color: #666;
	font-size: 14px;
	text-align: center;
	border-radius: 5px;
	background-color: #e6e6e6;
}

ul.op li.active {
	border: 1px dashed #ccc;
	border-radius: 5px;
	background-color: #fff;
}

.options div.oD {
	opacity: 0;
	filter: Alpha(opacity=0);
	transition: all 0.2s;
}

.options div.oD.show {
	opacity: 1;
	filter: Alpha(opacity=100);
	z-index: 2;
}

.options .Dcolor,.options .Dfont,.options .Dkey {
	position: absolute;
	top: 5px;
	left: 5px;
	right: 75px;
	bottom: 5px;
}

.oD ul {
	width: 100%;
	height: 100%;
	overflow-y: auto;
}

.oD ul li {
	display: inline-block;
	cursor: pointer;
	float: left;
	border: 1px solid #ddd;
}

.oD ul::-webkit-scrollbar-track-piece {
	background-color: rgba(0,0,0,0);
	border-left: 1px solid rgba(0,0,0,0);
}

.oD ul::-webkit-scrollbar {
	width: 5px;
	height: 13px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}

.oD ul::-webkit-scrollbar-thumb {
	background-color: rgba(0,0,0,0.2);
	background-clip: padding-box;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	min-height: 28px;
}

.oD ul::-webkit-scrollbar-thumb:hover {
	background-color: rgba(0,0,0,0.5);
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}

.oD.Dcolor ul li {
	width: 23px;
	height: 20px;
	margin: 1px;
}

.oD.Dfont ul li {
	padding: 2px 10px;
	margin: 2px 3px;
	line-height: 18px;
}

.oD.Dkey ul li {
	color: #666;
	padding: 3px 15px;
	margin: 3px 3px;
	line-height: 18px;
}

.rate {
	margin-bottom: 1em;
	border-top: 1px dashed #ccc;
	background-color: #fff;
}

.rate p {
	text-align: center;
	line-height: 28px;
	padding-bottom: 10px;
	border-bottom: 1px dashed #ccc;
}

.rate p span {
	display: block;
}

.rate p a {
	display: block;
	font-size: 14px;
	font-weight: 800;
}
.form-post {
	padding-top: 0.938em;
	padding-bottom: 0.938em;
}

.form-input p {
	text-align: left;
	padding: 0 5px;
}

.form-input p input {
	display: block;
	width: 100%;
	height: 42px;
	color: #666;
	font-size: 15px;
	text-indent: 15px;
	text-align: center;
	border: none;
	box-shadow: 1px 1px 3px 0 #ccc;
}

.form-input p input::-webkit-input-placeholder {
	font-size: 16px;
}

.form-input p button {
	display: inline-block;
	width: 47%;
	height:2.3em; 
	color: #fff;
	font-size: 15px;
	cursor: pointer;
	border: none;
	margin-top: 10px;
	transition: box-shadow 0.3s;
}



.form-input p button:active {
	text-indent: 2px;
	padding-top: 2px;
}

.form-input p button.reset {
	border-top-left-radius:2pc;
	border-bottom-left-radius:2pc;
    background-color: #ff585d;
}

.form-input p button.set {
	float: right;
    border-top-right-radius:2pc;
	border-bottom-right-radius:2pc;
	background-color: #1787e6;
}