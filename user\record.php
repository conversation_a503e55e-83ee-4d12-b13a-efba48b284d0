<?php
/**
 * 收支明细
**/
include("../includes/common.php");
$title='收支明细';
include './head.php';
if($islogin2==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");
$thtime=date("Y-m-d").' 00:00:00';
$lastday=date("Y-m-d",strtotime("-1 day")).' 00:00:00';
$income_today=$DB->getColumn("SELECT sum(point) FROM pre_points WHERE zid='{$userrow['zid']}' AND action='提成' AND addtime>'$thtime'");
$outcome_today=$DB->getColumn("SELECT sum(point) FROM pre_points WHERE zid='{$userrow['zid']}' AND action='消费' AND addtime>'$thtime'");
$income_lastday=$DB->getColumn("SELECT sum(point) FROM pre_points WHERE zid='{$userrow['zid']}' AND action='提成' AND addtime<'$thtime' AND addtime>'$lastday'");
$outcome_lastday=$DB->getColumn("SELECT sum(point) FROM pre_points WHERE zid='{$userrow['zid']}' AND action='消费' AND addtime<'$thtime' AND addtime>'$lastday'");
?>
<div class="wrapper">
	<div class="col-sm-12">
		   <div class="panel panel-default">
		   <div class="panel-heading font-bold" style="background-color: #9999CC;color: white;">收支明细</div>
<table class="table table-bordered">
<tbody>
<tr height="25">
<td align="center"><font color="#808080"><b><span class="glyphicon glyphicon-tint"></span>今日收益</b></br><?php echo round($income_today,2)?>元</font></td>
<td align="center"><font color="#808080"><b><i class="glyphicon glyphicon-check"></i>今日消费</b></br></span><?php echo round($outcome_today,2)?>元</font></td>
<td align="center"><font color="#808080"><b><span class="glyphicon glyphicon-tint"></span>昨日收益</b></br><?php echo round($income_lastday,2)?>元</font></td>
<td align="center"><font color="#808080"><b><i class="glyphicon glyphicon-check"></i>昨日消费</b></br></span><?php echo round($outcome_lastday,2)?>元</font></td>
</tr>
</tbody>
</table>
<div class="wrapper">
<form action="./record.php" method="GET" class="form-inline">
  <div class="form-group">
    <label><b>搜索</b></label>
	<select name="type" class="form-control" default="<?php echo @$_GET['type']?>"><option value="1">类型</option><option value="2">金额</option><option value="3">详情</option><option value="4">订单号</option></select>
    <input type="text" class="form-control" name="kw" placeholder="输入搜索内容" value="<?php echo @$_GET['kw']?>">
	<button type="submit" class="btn btn-info"><i class="fa fa-search"></i>&nbsp;搜索</button>
  </div>
</form>
</div>
      <div class="table-responsive">
        <table class="table table-striped">
          <thead><tr><th>ID</th><th>类型</th><th>金额</th><th>详情</th><th>时间</th></tr></thead>
          <tbody>
<?php

$link = '';
if(isset($_GET['kw']) && !empty($_GET['kw'])) {
	$type=intval($_GET['type']);
	$kw=daddslashes($_GET['kw']);
	if($type == 1) $sql = " AND `action`='$kw'";
	elseif($type == 2) $sql = " AND `point`='$kw'";
	elseif($type == 3) $sql = " AND `bz` LIKE '%$kw%'";
	elseif($type == 4) $sql = " AND `orderid`='$kw'";
	$link .= '&type='.$type.'&kw='.$kw;
}

$numrows=$DB->getColumn("SELECT count(*) from pre_points WHERE zid='{$userrow['zid']}'{$sql}");

$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($_GET['page'])?intval($_GET['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM pre_points WHERE zid='{$userrow['zid']}'{$sql} order by id desc limit $offset,$pagesize");
while($res = $rs->fetch())
{
echo '<tr><td><b>'.$res['id'].'</b></td><td>'.$res['action'].'</td><td><font color="'.(in_array($res['action'],array('提成','奖励','赠送','退款','退回','充值','加款'))?'red':'green').'">'.$res['point'].'</font></td><td>'.$res['bz'].'</td><td>'.$res['addtime'].'</td></tr>';
}
?>


          </tbody>
        </table>
      </div>
<?php
echo'<center><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="record.php?page='.$first.$link.'">首页</a></li>';
echo '<li><a href="record.php?page='.$prev.$link.'">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="record.php?page='.$i.$link.'">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="record.php?page='.$i.$link.'">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="record.php?page='.$next.$link.'">&raquo;</a></li>';
echo '<li><a href="record.php?page='.$last.$link.'">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></center>';
#分页
?>
   </div>
  </div>
 </div>
</div>
<?php include './foot.php';?>
</body>
</html>