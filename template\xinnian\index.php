﻿<?php
if(!defined('IN_CRONLITE'))exit();
$values=rand(1,19);
?>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"/>
  <title><?php echo $hometitle?></title>
  <meta name="keywords" content="<?php echo $conf['keywords']?>">
  <meta name="description" content="<?php echo $conf['description']?>">
  <link href="<?php echo $cdnpublic?>twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="<?php echo $cdnpublic?>font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
  <link href="<?php echo $cdnserver?>assets/css/nifty.min.css" rel="stylesheet">
  <link href="<?php echo $cdnserver?>assets/css/magic-check.min.css" rel="stylesheet">
  <link href="<?php echo $cdnserver?>assets/css/pace.min.css" rel="stylesheet">
  <link rel="stylesheet" href="<?php echo $cdnserver?>assets/css/common.css?ver=<?php echo VERSION ?>">
  <!--[if lt IE 9]>
    <script src="<?php echo $cdnpublic?>html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="<?php echo $cdnpublic?>respond.js/1.4.2/respond.min.js"></script>
  <![endif]-->
<?php echo $background_css?>
</head>
<body>
<div class="modal fade" align="left" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
        <h4 class="modal-title" id="myModalLabel"><?php echo $conf['sitename']?></h4>
      </div>
      <div class="modal-body">
	  <?php echo $conf['modal']?>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">知道啦</button>
      </div>
    </div>
  </div>
</div>
<br/>
<div class="col-xs-12 col-sm-10 col-md-8 col-lg-6 center-block" style="float: none;">


<!--这里是网站的logo部分-->
<div class="panel panel-success">
<div class="panel-body" style="text-align: center;"><img src="<?php echo $logo;?>" style="max-width: 100%;"></div></div>
<!--logo部分结束-->

<div class="panel panel-danger">
	<div class="panel-heading"><h3 class="panel-title" ><font color="#FFFFFF"><i class=""></i><b> <script type="text/javascript">
var now=(new Date()).getHours();
if(now>0&&now<=6){
document.write("❤熬夜对身体不好哦 快睡觉！");
}else if(now>6&&now<=11){
document.write("❤早上好 心情好来下一单吧~");
}else if(now>11&&now<=14){
document.write("❤停下手中的工作 去吃饭~");
}else if(now>14&&now<=18){
document.write("❤累了一上午了 休息会吧~");
}else{
document.write("❤晚上好 下一单醒来有惊喜哟~");
}
</script></font> </b></h3></div>

 <table class="table table-bordered">
							<tbody>
								</tbody></table>
   <?php echo $conf['anounce']?>

</div>
<div class="tab-content">
	<div id="demo-tabs-box-1" class="tab-pane fade active in">
		<div class="panel panel-danger">
			<div class="panel-heading">
				<h3 class="panel-title"><font color="#fff"><i class="fa fa-shopping-cart"></i>&nbsp;&nbsp;<b>自助下单</b></font><span class="pull-right"><a href="./user/" class="btn btn-warning btn-rounded"><i class="fa fa-user"></i> 用户中心</a></span></h3>
			</div>
	<ul class="nav nav-tabs">
		<li class="active"><a href="#onlinebuy" data-toggle="tab"><i class="fa fa-shopping-cart"></i> 下单</a></li>
		<li><a href="#query" data-toggle="tab" id="tab-query"><i class="fa fa-search"></i> 查单</a></li>
		<?php if($conf['fenzhan_buy']==1){?><li><a href="#fenzhan" data-toggle="tab"><i class="fa fa-sitemap"></i> 分站</a></li><?php }?>
		<?php if(!empty($conf['daiguaurl'])){?><li><a href="./?mod=daigua"><i class="fa fa-rocket"></i> 代挂</a></li><?php }?>
		<?php if($conf['gift_open']==1){?><li><a href="#gift" data-toggle="tab"><i class="fa fa-gift"></i> 抽奖</a></li><?php }?>
	</ul>
	<div class="modal-body">
		<div id="myTabContent" class="tab-content">
		<div class="tab-pane fade in active" id="onlinebuy">
<?php include TEMPLATE_ROOT.'default/shop.inc.php'; ?>
		</div>
		<div class="tab-pane fade in" id="query">
			<div class="alert alert-danger" <?php if(empty($conf['gg_search'])){?>style="display:none;"<?php }?>><?php echo $conf['gg_search']?></div>
			<div class="form-group">
				<div class="input-group">
				<div class="input-group-btn">
					<select class="form-control" id="searchtype" style="padding: 6px 4px;width:90px"><option value="0">下单账号</option><option value="1">订单号</option></select>
				</div>
				<input type="text" name="qq" id="qq3" value="<?php echo $qq?>" class="form-control" placeholder="请输入要查询的内容（留空则显示最新订单）" onkeydown="if(event.keyCode==13){submit_query.click()}" required/>
				<span class="input-group-btn"><a tabindex="0" class="btn btn-default" role="button" data-container="body" data-toggle="popover" data-trigger="focus" data-placement="top" title="查询内容是什么？" data-content="请输入您下单时，在第一个输入框内填写的信息。如果您不知道下单账号是什么，可以不填写，直接点击查询，则会根据浏览器缓存查询！"><i class="glyphicon glyphicon-exclamation-sign"></i></a></span>
			</div></div>
			<input type="submit" id="submit_query" class="btn btn-danger btn-block" value="立即查询">
			<div id="result2" class="form-group" style="display:none;">
				<div class="table-responsive">
				<table class="table table-striped">
				<thead><tr><th>下单账号</th><th>商品名称</th><th>数量</th><th class="hidden-xs">购买时间</th><th>状态</th><th>操作</th></tr></thead>
				<tbody id="list">
				</tbody>
				</table>
				</div>
			</div>
		</div>

		<div class="tab-pane fade in" id="fenzhan">
		  <div class="row"> 
   <div class="col-sm-12 col-md-6"> 
    <div class="panel panel-primary"> 
     <div class="panel-heading"> 
      <div class="panel-title">
        普及版分站 
      </div> 
     </div> 
     <li class="list-group-item"> 限时超级低价搭建代理分站<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 可享受分站内部超低代理价格<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 可以赚取每一个用户下单的提成<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 可以自定义售卖的商品价格<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 赚取下级分站的每笔交易的提成<span class="badge badge-danger"><i class="fa fa-close"></i></span> </li> 
     <li class="list-group-item"> 无限免费搭建下级代理分站<span class="badge badge-danger"><i class="fa fa-close"></i></span> </li> 
     <li class="list-group-item"> 分站站长专属的内部售后交流群<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 分站满<?php echo $conf["tixian_min"]?>元即可申请提现<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <div class="list-group-item list-group-item-warning"> 
      <i class="fa fa-rmb fa-lg grey" style="width: 18px; text-align: center;"></i> 
      <span class="m-left-xs">开通价格</span> 
      <span class="badge badge-info"><b><?php echo $conf["fenzhan_price"]?></b>元</span> 
     </div> 
     <a data-toggle="modal" href="./user/regsite.php?kind=0" target="_blank" class="list-group-item list-group-item-info text-center"><strong>马上开通</strong></a> 
    </div> 
   </div> 
   <div class="col-sm-12 col-md-6"> 
    <div class="panel panel-primary"> 
     <div class="panel-heading"> 
      <div class="panel-title">
        专业版分站 
      </div> 
     </div> 
     <li class="list-group-item"> 限时超级低价搭建代理分站<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 可享受分站内部超低代理价格<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 可以赚取每一个用户下单的提成<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 可以自定义售卖的商品价格<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 赚取下级分站的每笔交易的提成<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 无限免费搭建下级代理分站<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 分站站长专属的内部售后交流群<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <li class="list-group-item"> 分站满<?php echo $conf["tixian_min"]?>元即可申请提现<span class="badge badge-success"><i class="fa fa-check"></i></span> </li> 
     <div class="list-group-item list-group-item-warning"> 
      <i class="fa fa-rmb fa-lg grey" style="width: 18px; text-align: center;"></i> 
      <span class="m-left-xs">开通价格</span> 
      <span class="badge badge-info"><b><?php echo $conf["fenzhan_price2"]?></b>元</span> 
     </div> 
     <a data-toggle="modal" href="./user/regsite.php?kind=1" target="_blank" class="list-group-item list-group-item-info text-center"><strong>马上开通</strong></a> 
    </div> 
   </div> 
  </div> 
</div>

<?php if($conf['gift_open']==1){?>
<div class="tab-pane fade in" id="gift">
	<div class="panel-body text-center">
	<div id="roll">点击下方按钮开始抽奖</div>
	<hr>
	<p>
	<a class="btn btn-info" id="start" style="display:block;">开始抽奖</a>
	<a class="btn btn-danger" id="stop" style="display:none;">停止</a>
	</p> 
	<div id="result"></div><br/>
	<div class="giftlist" style="display:none;"><strong>最近中奖记录</strong><ul id="pst_1"></ul></div>
	</div>
</div>
<?php }?>

</div>
</div>

</div>

<?php if($conf['articlenum']>0){
$limit = intval($conf['articlenum']);
$rs=$DB->query("SELECT id,title FROM pre_article WHERE active=1 ORDER BY top DESC,id DESC LIMIT {$limit}");
$msgrow=array();
while($res = $rs->fetch()){
	$msgrow[]=$res;
}
$class_arr = ['danger','warning','primary','success','info'];
$i=0;
?>
<!--文章列表-->
<div class="panel panel-danger" <?php if($conf['bottom']==''){?>style="display:none;"<?php }?>>
<div class="panel-heading"><h3 class="panel-title"><font color="#fff"><i class="fa fa-newspaper-o"></i>&nbsp;&nbsp;<b>文章列表</b></font></h3></div>
	<?php foreach($msgrow as $row){
	echo '<a target="_blank" style="color:blue" class="list-group-item" href="'.article_url($row['id']).'"><span class="btn btn-'.$class_arr[($i++)%5].' btn-xs">'.$i.'</span>&nbsp;'.$row['title'].'</a>';
	}?>
	<a href="<?php echo article_url()?>" title="查看全部文章" class="btn-default btn btn-block" target="_blank">查看全部文章</a>
</div>
<!--文章列表-->
<?php }?>

<?php if(!$conf['hide_tongji']){?>
<div class="row">
	<div class="col-lg-6">
	<div class="panel panel-success panel-colorful">
			<div class="pad-all media">
				<div class="media-left">
					<i class="demo-pli-coin icon-3x icon-fw"></i>
				</div>
				<div class="media-body">
					<p class="h3 text-light mar-no media-heading"><span id="count_money"></span>元</p>
					<span>累计交易金额</span>
				</div>
			</div>
			<div class="progress progress-xs progress-success mar-no">
				<div class="progress-bar progress-bar-light" style="width: 100%"></div>
			</div>
			<div class="pad-all text-sm">
				今天交易金额 <span class="text-semibold" id="count_money1"></span> 元
			</div>
		</div>
	</div>
	<div class="col-lg-6">
	<div class="panel panel-info panel-colorful">
			<div class="pad-all media">
				<div class="media-left">
					<i class="demo-pli-add-cart icon-3x icon-fw"></i>
				</div>
				<div class="media-body">
					<p class="h3 text-light mar-no media-heading"><span id="count_orders"></span>条</p>
					<span>累计订单总数</span>
				</div>
			</div>
			<div class="progress progress-xs progress-dark-base mar-no">
				<div class="progress-bar progress-bar-light" style="width: 100%"></div>
			</div>
			<div class="pad-all text-sm bg-trans-dark">
				今天订单总数 <span class="text-semibold" id="count_orders2"></span> 条
			</div>
		</div>
	</div>
</div>
<?php }?>

<div class="panel panel-danger" <?php if($conf['bottom']==''){?>style="display:none;"<?php }?>>
<div class="panel-heading"><h3 class="panel-title"><font color="#fff"><i class="fa fa-skyatlas"></i>&nbsp;&nbsp;<b>站点助手</b></font></h3></div>
<?php echo $conf['bottom']?>
</div>
</div>

<p style="text-align:center"><span style="font-weight:bold">CopyRight <i class="fa fa-heart text-danger"></i> <?php echo date("Y")?> <a href="/"><?php echo $conf['sitename']?></a></span><br/><?php echo $conf['footer']?></p>
</div>

</div>
<!--音乐代码-->
<div id="audio-play" <?php if(empty($conf['musicurl'])){?>style="display:none;"<?php }?>>
  <div id="audio-btn" class="on" onclick="audio_init.changeClass(this,'media')">
    <audio loop="loop" src="<?php echo $conf['musicurl']?>" id="media" preload="preload"></audio>
  </div>
</div>
<!--音乐代码-->

<script src="<?php echo $cdnpublic?>jquery/1.12.4/jquery.min.js"></script>
<script src="<?php echo $cdnpublic?>jquery.lazyload/1.9.1/jquery.lazyload.min.js"></script>
<script src="<?php echo $cdnpublic?>twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script src="<?php echo $cdnpublic?>jquery-cookie/1.4.1/jquery.cookie.min.js"></script>
<script src="<?php echo $cdnpublic?>layer/2.3/layer.js"></script>

<script type="text/javascript">
var isModal=<?php echo empty($conf['modal'])?'false':'true';?>;
var homepage=true;
var hashsalt=<?php echo $addsalt_js?>;
$(function() {
	$("img.lazy").lazyload({effect: "fadeIn"});
});
</script>
<script src="assets/js/main.js?ver=<?php echo VERSION ?>"></script>
<script src="assets/js/snow.js"></script>
<?php if($conf['classblock']==1 || $conf['classblock']==2 && checkmobile()==false)include TEMPLATE_ROOT.'default/classblock.inc.php'; ?>
</body>
</html>