.fui-notice {

}

.fui-notice:before,
.fui-notice:after {
    border: 0;
}

.fui-line-diy {
    height: auto;
    display: block;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    padding: 10px 0;
}

.fui-line-diy .line {
    height: 0px;
    width: 100%;
    border-top: 1px solid #000000;
}

.fui-line-diy:before {
    border: 0;
    height: 0;
}

.fui-title {
    color: #888;
    font-size: 14px;
    padding: 0 0.7rem;
}

.fui-picture {
    display: block;
    margin: 0;
    padding: 0;
    height: auto;
    overflow: hidden;
}

.fui-picture img {
    display: block;
    width: 100%;
}

.fui-goods-group {
    background: none;
}

.fui-goods-item .image {


    float: left;
    background-size: 100%;
    position: relative;
    overflow: hidden;
}


.fui-goods-item .detail .price .buy i {
    font-size: 0.7rem;
}

.fui-goods-item .detail .price .buy {
    display: inline-block;
    height: 1.1rem;
    color: #ff5555;
    line-height: 1rem;
    background: #fff;
    padding: 0rem 0.35rem;
    width: auto;
    border-radius: 0.1rem;
    border: 1px solid transparent;
}

.fui-goods-item .detail .price .cycelbuy {
    display: inline-block;
    height: 1.1rem;
    color: #ff5555;
    line-height: 1rem;
    border-radius: 0;
    background: #fff;
    padding: 0rem 0.35rem;
    width: auto;
    border-radius: 0.1rem;
    border: 1px solid #ff5555;
    font-size: 0.6rem;
}

.fui-goods-item .detail .price .buy.buybtn-2 {
    background: #ff5555;
    color: #fff;
}

.fui-goods-item .detail .price .buy.buybtn-3 {
    background: #ff5555;
    color: #fff;
    vertical-align: middle;
    width: 1.1rem;
    padding: 0;
    text-align: center;
    line-height: 1.1rem;
}

.fui-goods-item .detail .price .buy.buybtn-4, .fui-goods-item .detail .price .buy.buybtn-5 {
    vertical-align: middle;
    width: 1.1rem;
    padding: 0.01rem 0.01rem 0 0.02rem;


    line-height: 1rem;
    border-radius: 50%;
    background: #fff;
    border: 1px solid #ff5555;
    text-align: center;
}

.fui-goods-item .detail .price .buy.buybtn-4 i, .fui-goods-item .detail .price .buy.buybtn-5 i {
    color: #ff5555;
}

.fui-goods-item .detail .price .buy.buybtn-6 {
    vertical-align: middle;
    width: 1.1rem;
    padding: 0.01rem 0 0;
    text-align: center;
    line-height: 1rem;
    border-radius: 50%;
    color: #fff;
}

.fui-goods-item .image .goodsicon {
    position: absolute;
    height: auto;
    width: auto;
    display: block;
    overflow: hidden;
}

.fui-goods-group .image.triangle {
    position: relative;
    overflow: hidden;
}

.fui-goods-group .image.triangle:before {
    content: attr(data-text);
    position: absolute;
    width: 3.39rem;
    height: 2.4rem;
    background: #ff5555;
    z-index: 0;
    transform-origin: left bottom;
    -webkit-transform-origin: left bottom;
    transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    color: #fff;
    line-height: 3.7rem;
    font-size: 0.6rem;
    text-align: center;
}

.fui-goods-group .image.rectangle {
    position: relative;
}

.fui-goods-group .image.rectangle:before {
    content: attr(data-text);
    position: absolute;
    width: 2.25rem;
    height: 1rem;
    background: #04a6f8;
    z-index: 0;
    text-align: center;
    color: #fff;
    font-size: 0.6rem;
    line-height: 1rem;
}

.fui-goods-group .image.circle {
    position: relative;
}

.fui-goods-group .image.circle:before {
    content: attr(data-text);
    position: absolute;
    width: 1.6rem;
    height: 1.6rem;
    background: #ffb805;
    z-index: 0;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    font-size: 0.6rem;
    line-height: 0.6rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    -webkit-align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
}

.fui-goods-group .image.square {
    position: relative;
}

.fui-goods-group .image.square:before {
    content: attr(data-text);
    position: absolute;
    width: 1.6rem;
    height: 1.6rem;
    background: #19dbab;
    z-index: 0;
    text-align: center;
    color: #fff;
    font-size: 0.6rem;
    line-height: 0.6rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    -webkit-align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
}

.fui-goods-group .image.echelon {
    position: relative;
}

.fui-goods-group .image.echelon:before {
    content: attr(data-text);
    position: absolute;
    height: 1rem;
    width: 3.4rem;
    background: #ff7104;
    top: 1.4rem;
    color: #fff;
    font-size: 0.6rem;
    line-height: 1rem;
    text-align: center;
    transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
}

.fui-goods-item .image .goodsicon img {
    display: block;
    max-width: 100%;
}

.fui-goods-item .image .goodsicon.left {
    left: 0;
}

.fui-goods-item .image .goodsicon.right {
    right: 0;
}

.fui-goods-item .image .goodsicon.top {
    top: 0;
}

.fui-goods-item .image .goodsicon.bottom {
    bottom: 0;
}

.fui-swipe-page {
    height: 14px;
    padding: 0;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 1;
    line-height: 14px;
}

.fui-swipe-page.left {
    text-align: left;
}

.fui-swipe-page.center {
    text-align: center;
}

.fui-swipe-page.right {
    text-align: right;
}

.fui-swipe-page.rectangle .fui-swipe-bullet {
    height: 12px;
    width: 20px;
}

.fui-swipe-page.square .fui-swipe-bullet {
    height: 12px;
    width: 12px;
}

.fui-swipe-page.round .fui-swipe-bullet {
    height: 12px;
    width: 12px;
    border-radius: 12px;
}

.fui-swipe-page .fui-swipe-bullet {
    display: inline-block;
    margin: 0 4px 0 0;
    border-radius: 0;
}

.searchbtn {
    background: none;
    border: 0;
}

.searchbar,
.fui-searchbar {
    height: auto;
}

.searchbar {
    padding: 0;
}

.searchbar.right {
    text-align: right;
}

.search-input.radius {
    border-radius: 5px;
}

.search-input.round {
    border-radius: 30px;
}

.fui-picturew {
    height: auto;
    display: block;
    overflow: hidden;
    width: 100%;
    padding: 0 2px;
    margin-bottom: -1px;

}

.fui-picturew .item {
    height: auto;
    display: block;
    float: left;
    margin-bottom: -1px;
}

.fui-picturew.row-2 .item {
    width: 50% !important;
}

.fui-picturew.row-3 .item {
    width: 33.33% !important;
}

.fui-picturew.row-4 .item {
    width: 25% !important;
}

.fui-picturew.row-5 .item {
    width: 20% !important;

}

.fui-picturew .item a {
    display: block;
    width: 100.6%;
}

.fui-picturew .item img {
    display: block;
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    border: 0;
    padding: 0;
    outline: none;

}


.fui-picturew .item .image {
    position: relative;
}

.fui-picturew .item .image .title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    line-height: 1rem;
    color: #fff;
    padding: 0.4rem 0.15rem 1px;
    font-size: 0.7rem;
    background: -moz-linear-gradient(bottom, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);
    background: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);
    background: -o-linear-gradient(bottom, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);
    background: -ms-linear-gradient(bottom, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#00000000, endColorstr=#99000000, gradientType='0');
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.fui-picturew .item .text {
    font-size: 0.7rem;
    height: 1rem;
    line-height: 1.2rem;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-left: 0.15rem;
}


.diymenu-page {
    height: 100%;
    width: 100%;
    line-height: 600px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
}

.diymenu {
    height: 2.5rem;
    width: 100%;
    position: absolute;
    z-index: 1;
    left: 0;
    bottom: 0;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

.diymenu .item {
    height: 2.5rem;
    width: 25%;
    display: block;
    background: #fff;
    float: left;
    position: relative;
    cursor: pointer;
}

.diymenu .item.item-col-1 {
    width: 100%;
}

.diymenu .item.item-col-2 {
    width: 50%;
}

.diymenu .item.item-col-3 {
    width: 33.33%;
}

.diymenu .item.item-col-4 {
    width: 25%;
}

.diymenu .item.item-col-5 {
    width: 20%;
}

.diymenu .item .child {
    display: block;
    height: auto;
    min-width: 90px;
    max-width: 130px;
    background: #eee;
    position: absolute;
    bottom: 60px;
    left: 50%;
    margin-left: -45px;
    z-index: 996;
    border-radius: 3px;
    border: 1px solid #ccc;

    -moz-transition-duration: 0.4s;
    -webkit-transition-duration: 0.4s;
    transition-duration: 0.4s;
    -webkit-transform: translate3d(0, 150%, 0);
    transform: translate3d(0, 150%, 0);
}

.diymenu .item .child.in {
    -moz-transition-duration: 0.4s;
    -webkit-transition-duration: 0.4s;
    transition-duration: 0.4s;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.diymenu .item .child a {
    clear: both;
    display: block;
    padding: 8px;
    font-size: 13px;
    position: relative;
    color: #666;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.diymenu .item .child a:after {
    content: '';
    position: absolute;
    left: 10px;
    right: 10px;
    top: 0;
    height: 0;
    border-top: 1px solid #ccc;
}

.diymenu .item .child a:first-child:after {
    border: none;
}

.diymenu .item .child .arrow {
    width: 18px;
    height: 18px;
    color: #ccc;
    position: absolute;
    bottom: -10px;
    left: 50%;
    margin-left: -9px;
}

.diymenu .item .child .arrow:after,
.diymenu .item .child .arrow:before {
    position: absolute;
    bottom: 3px;
    left: 3px;
    content: "";
    height: 12px;
    width: 12px;
    background: #000;
    transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    z-index: 997;
}

.diymenu .item .child .arrow:after {
    bottom: 4.5px;
    background: #ccc;
    z-index: 998;
}

.diymenu .item .inner {
    height: 100%;
    width: 100%;
    background: #fff;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 997;
    text-align: center;
    color: #fff;
    padding: 0;
}

.diymenu .item .inner.left .icon {
    line-height: 1.5rem;
    margin-right: 0.1rem;
}

.diymenu .item .inner.left {
    display: flex;
    align-items: center;
    justify-content: center;
}

.diymenu .item .inner.left .text.left {
    margin-top: 0;
}

.diymenu .item .inner:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 0;
    width: 100%;
    border-top: 1px solid rgba(0, 0, 0, 0);
}

.diymenu .item .inner:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    border-left: 1px solid rgba(0, 0, 0, 0);
}

.diymenu .item:first-child .inner:after {
    border-left: 0;
}

.diymenu .item .inner .icon {
    display: block;
    font-size: 1rem;
    height: 1.5rem;
    line-height: 1.7rem;
    color: #f90;
}

.diymenu .item .inner .text {
    display: block;
    font-size: 0.55rem;
    color: #999;
    overflow: hidden;
    margin: 0 2px;
}

.diymenu .item .inner .icon.left,
.diymenu .item .inner .text.left {
    display: inline-block;
}

.diymenu .item .inner .text.left {
    margin-top: 10px;
    font-size: 14px;
}

.diymenu .item .inner .icon.left {
    font-size: 18px;

}

.diymenu .item .inner.image {
    padding: 1px 0 2px 0;
}

.diymenu .item .inner.image img {
    display: inline-block;
    height: 100%;
    max-width: 100%;
}

.diymenu .item .inner .badge {
    background: red none repeat scroll 0 0;
    border-radius: 0.5rem;
    color: white;
    font-size: 0.6rem;
    height: 0.8rem;
    left: 50%;
    line-height: 0.8rem;
    margin-left: 0.1rem;
    min-width: 0.8rem;
    padding: 0 0.2rem;
    position: absolute;
    top: 0.1rem;
    vertical-align: top;
    z-index: 100;
}

.diy-richtext a {
    color: #337ab7;
}

.diy-richtext ul,
.diy-richtext li {
    list-style: inherit;
    list-style-position: outside;
}

.diy-richtext ul {
    -webkit-padding-start: 30px;
}

.diy-richtext p {
    -webkit-margin-before: 0;
    -webkit-margin-after: 0;
}

.diy-richtext img {
    display: inline-block;
    max-width: 100%;
}

.diy-richtext video {
    display: block;
    width: 100%;
}

.fui-list {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding: 0.4rem 0.6rem;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    overflow: hidden;

}

.fui-list.align-start {
    -webkit-box-align: start;
    -webkit-align-items: start;
    -ms-flex-align: start;
    align-items: start
}

.fui-list:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: .5rem;
    height: 1px;
    border-top: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: .6rem;
}

.fui-list:first-child:before {
    display: none;
}

.fui-list:active {
    background: #ececec;
}

.fui-list.noclick:active {
    background: #fff;
}

.fui-list a {
    color: #666;
}

.fui-list-media {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-shrink: 0;
    -ms-flex: 0 0 auto;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    -webkit-box-lines: single;
    -moz-box-lines: single;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
    box-sizing: border-box;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    margin-right: .5rem;
    color: #aaa;
    position: relative;
}

.fui-list-media .title {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 0.7rem;
    height: 1rem;
    right: 0;
    left: 0;
    line-height: 1rem;
    font-size: 0.6rem;
    padding: 0 0.15rem;
    position: absolute;
    bottom: 0;
    text-align: center;
    vertical-align: top;
    z-index: 100;
}

.fui-list-media img {
    width: 2.5rem;
}

.fui-list-media img.round {
    border-radius: .3rem;
}

.fui-list-media .badge {
    background: red none repeat scroll 0 0;
    border-radius: 0.5rem;
    color: white;
    font-size: 0.7rem;
    height: 0.9rem;
    right: -.35rem;
    line-height: 0.9rem;
    min-width: 0.9rem;
    padding: 0 0.15rem;
    position: absolute;
    top: -.3rem;
    text-align: center;
    vertical-align: top;
    z-index: 100;
}

.fui-remark:after {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 0.4rem;
    width: 0.4rem;
    border-width: 1px 1px 0 0;
    border-color: #b2b2b2;
    border-style: solid;
    position: relative;
    top: -1px;
    margin-left: .1em;
}


.fui-block-group {
    background: #fff;
    border-bottom: 1px solid #ececec;
    position: relative;
    margin-top: .5rem
}

.fui-block-group .fui-block-child {
    height: auto;
    float: left;
    padding: 0.4rem 0;
    background: #fff;
    transition: background-color 300ms;
    -webkit-transition: background-color 300ms;
    position: relative;
}

.fui-block-group .fui-block-child:before {
    content: "";
    width: 0px;
    border-right: 1px solid #ececec;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
}

.fui-block-group .fui-block-child:after {
    content: "";
    height: 0px;
    border-bottom: 1px solid #ececec;
    position: absolute;
    right: 0;
    left: 0;
    bottom: 0;
}

.fui-block-group.col-2 .fui-block-child {
    width: 50%
}

.fui-block-group.col-3 .fui-block-child {
    width: 33.3%
}

.fui-block-group.col-4 .fui-block-child {
    width: 25%
}

.fui-block-group.col-5 .fui-block-child {
    width: 20%
}

.fui-block-group .fui-block-child:active {
    background: #ececec;
}

.fui-block-group .fui-block-child .icon {
    height: 2.7rem;
    padding-top: 1rem;
    text-align: center;
    font-size: 1.4rem;
    line-height: 1.5rem;
}

.fui-block-group .fui-block-child .icon i {
    font-size: 1.8rem;
}

.fui-block-group .fui-block-child .title {
    height: 1rem;
    font-size: 0.6rem;
    line-height: 1.5rem;
    text-align: center;
    color: #666;
}

.fui-block-group .fui-block-child .title.bigsize {
    font-size: 0.7rem;
}

.fui-block-group .fui-block-child .text {
    height: 1.2rem;
    font-size: 0.5rem;
    text-align: center;
    color: #666;
    line-height: 1rem;
}

.fui-block-group .fui-block-child .text span {
    color: #feb312;
}

.fui-block-group .fui-block-child .text.remark {
    color: #a9a9a9;
    font-size: 0.6rem;
    line-height: 1.2rem;
}

.fui-block-group .fui-block-child .num {
    height: 0.9rem;
    text-align: center;
    font-size: 0.8rem;
    color: #fb6665;
    line-height: 1.4rem;
}

.fui-block-group .fui-block-dots {
    height: 0.9rem;
    text-align: center;
    line-height: 0.6rem;
}

.fui-block-group .fui-block-dots a {
    height: 0.3rem;
    width: 0.3rem;
    background: #e6e6e6;
    border-radius: 0.4rem;
    display: inline-block;
}

.fui-block-group .fui-block-dots a.on {
    background: #fa5453;
}


.fui-block-group .fui-block-child.new {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    padding-left: .6rem;
    height: 3.7rem
}


.fui-block-group .fui-block-child .text.new {
    height: auto;
    line-height: normal;
    margin-left: .6rem;
    font-size: .6rem;
    color: #999;
    text-align: left
}

.fui-cell-group .fui-cell .fui-cell-icon .text-yellow {
    color: #ff8000
}

.fui-block-group .fui-block-child .title {
    height: auto;
    line-height: normal;
    font-size: .7rem;
    color: #000;
    text-align: center
}

.fui-block-group .fui-block-child .text.new .title {
    text-align: left
}

.fui-block-group .fui-block-child .text span {
    color: #999
}

.fui-block-group .fui-block-child .icon {
    padding-top: 0;
    line-height: 2.7rem
}

.fui-block-group .fui-block-child .icon i {
    font-size: 1.3rem
}


.headinfo-m {
    position: relative;
    height: auto;
    background: #fe5455;
    padding-top: 20px;
    border-top: 1px #b2423f solid;
    border-bottom: 1px #fa9d9d solid;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
}

.headinfo-m .setbtn {
    height: 24px;
    width: 24px;
    position: absolute;
    top: 4px;
    right: 10px;
    font-size: 24px;
    color: #fff;
    line-height: 24px;
    text-align: center;
}

.headinfo-m .child {
    width: 30%;
    height: auto;
    padding-bottom: 24px;
    text-align: center;
}

.headinfo-m .child.userinfo {
    width: 40%;
    color: #fff;
}

.headinfo-m .child.userinfo .face {
    height: 56px;
    width: 56px;
    background: #fff;
    margin: auto;
    border-radius: 56px;
    border: 2px solid #fff;
}

.headinfo-m .child.userinfo .face img {
    height: 52px;
    width: 52px;
    border-radius: 52px;
    display: block;
}

.headinfo-m .child.userinfo .face.radius {
    border-radius: 10px;
}

.headinfo-m .child.userinfo .face.radius img {
    border-radius: 10px;
}

.headinfo-m .child.userinfo .name {
    height: 20px;
    padding-top: 4px;
    font-size: 12px;
    color: inherit;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.headinfo-m .child.userinfo .level {
    font-size: 12px;
    color: inherit;
    text-align: center;
}

.headinfo-m .child .title {
    padding-top: 24px;
    font-size: 14px;
    color: #fff;
    text-align: center;
}

.headinfo-m .child .num {
    font-size: 14px;
    color: #fef31f;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.headinfo-m .child .btn {
    width: 52px;
    height: 20px;
    padding: 0;
    margin: 4px auto 0;
    color: #fff;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    border-radius: 20px;
    border: 1px solid #fff;
}

.page-commission-index .headinfo {
    border: 0;
}

.headinfo.commission.style3
.headinfo.commission.style3 {
    height: auto;
    background: #fea23d;
}

.headinfo.commission.style3 .userinfo {
    min-height: 4rem;
    padding: 0.3rem 0.6rem;
    border-bottom: 1px solid #fea23d;
    position: relative
}

.headinfo.commission.style3 .userinfo .fui-list {
    padding: 0rem;
    padding-top: .5rem;
    padding-bottom: .5rem;
}

.headinfo.commission.style3 .userinfo .fui-list:active {
    background: transparent
}

.headinfo.commission.style3 .userinfo .fui-list-media {
    margin-right: .7rem
}

.headinfo.commission.style3 .userinfo .fui-list-media img {
    height: 3rem;
    width: 3rem;
    border-radius: 3rem;
}

.headinfo.commission.style3 .userinfo .fui-list .fui-list-info .title {
    color: #fff;
    font-size: 0.7rem;
    line-height: 1rem;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.headinfo.commission.style3 .userinfo .fui-list .fui-list-info .subtitle {
    color: #fff;
    font-size: 0.55rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: auto;
    display: inline-block;
    border: .025rem solid #fff;
    border-radius: .85rem;
    line-height: .85rem;
    max-width: 100%;
    padding: 0 .3rem;
}

.headinfo.commission.style3 .userinfo .fui-list .fui-list-info .text {
    color: #fff;
    font-size: 0.55rem;
    line-height: 1rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.headinfo.commission.style3 .userinfo .setbtn {
    position: absolute;
    height: 1.4rem;
    width: 1.2rem;
    font-size: 1.2rem;
    color: #fff;
    line-height: 1.4rem;
    text-align: center;
    right: .5rem;
    top: .5rem;
}

.userblock.commission {
    margin-top: .5rem
}

.userblock.commission .line.total {
    height: 4.15rem;
    padding: .8rem .6rem
}

.userblock.commission .line.total.new {
    height: 4.15rem;
    background: #fff;
    padding: .8rem .6rem
}

.userblock.commission .num {
    color: #ff8000;
    font-size: 1rem
}

.userblock.commission .line.total .title {
    color: #000;
    font-size: .6rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.userblock.commission .line.usable.new {
    height: 4.15rem;
    background: #fff;
    padding: 0 .6rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}

.userblock.commission .line.usable .text {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.userblock.commission .line.usable:after {
    content: " ";
    position: absolute;
    left: 0.5rem;
    right: 0.5rem;
    top: -1px;
    height: 1px;
    border-top: 1px solid #ebebeb;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.userblock.commission .line.usable .title {
    margin-right: .2rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    font-size: .6rem;
    color: #000;
}

.userblock.commission .btn.btn-warning.external, .userblock.commission .btn.btn-warning.disabled {
    border-radius: .7rem;
    height: 1.4rem;
    margin-right: 0;
    font-size: .65rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    border: 0;
    padding-top: .1rem
}


.swiper-pagination-bullet-active {
    background: #000;
    opacity: 0.5;
}

.swiper-button-white {
    background: rgba(0, 0, 0, 0.3);
    background-size: 70%;
    background-repeat: no-repeat;
    background-position: center;
}

.swiper-pagination {
    position: relative;
}

.swiper-container-horizontal > .swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction {
    bottom: 5px;
}

.swiper-container-autoheight .swiper-wrapper {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.fui-picturew .fui-picturew-pagination {
    height: 10px;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 8px;
    text-align: center;
    padding: 0;
    margin: 0;
    line-height: 10px;
}

.fui-picturew .fui-picturew-pagination a {
    height: 8px;
    width: 8px;
    background: #000;
    border-radius: 8px;
    display: inline-block;
    opacity: 0.3;
}

.fui-picturew .fui-picturew-pagination a.active {
    opacity: 0.5;
}

.diy-coupon {
    padding: 0 0.3rem;
    height: auto;
    overflow: hidden;

}

.diy-coupon .diy-coupon-item {
    width: 33.33%;
    float: left;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.diy-coupon .diy-coupon-item .inner {

    height: 100%;
    border-radius: 0;
    border: none;
    background: #fd5454;
    color: #fff;
    font-size: 0.55rem;
    line-height: 0.6rem;
    position: relative;
    margin: 0.3rem;
    text-align: center;
}

.diy-coupon.col-2 .diy-coupon-item {
    width: 50%;
}

.diy-coupon.col-3 .diy-coupon-item {
    width: 33.33%;
}

.diy-coupon .diy-coupon-item .name {
    font-size: 1rem;
    line-height: 1.75rem;
    position: relative;
}

.diy-coupon .diy-coupon-item .desc {
    font-size: 0.6rem;
    line-height: 1.6rem;
}

.diy-coupon .diy-coupon-item .receive {
    width: 3.3rem;
    border-radius: 1rem;
    border: 1px solid #fff;
    height: 1rem;
    line-height: 1rem;
    margin: 0 auto 0.4rem;
}

.diy-coupon .diy-coupon-item i {
    position: absolute;
    height: 0.7rem;
    width: 0.7rem;
    border-radius: 50%;
    top: 50%;
    margin-top: -0.35rem;
}

.diy-fixedsearch {
    height: 44px;
    overflow: hidden;
    position: relative;
}

.diy-fixedsearch.fixed {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.diy-fixedsearch + div {

}

.diy-fixedsearch .background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000000;
    opacity: 0.5;
    z-index: 1;
}

.diy-fixedsearch .inner {
    position: relative;
    z-index: 2;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    color: #fff;
    height: 44px;
    padding: 8px;
}

.diy-fixedsearch .inner .leftnav {
    height: 28px;
    line-height: 28px;
}

.diy-fixedsearch .inner .rightnav {
    height: 28px;
    line-height: 28px;
}

.diy-fixedsearch .inner .leftnav img,
.diy-fixedsearch .inner .rightnav img {
    height: 28px;
    width: auto;
    display: block;
}

.diy-fixedsearch .inner .leftnav .icon,
.diy-fixedsearch .inner .rightnav .icon {
    font-size: 24px;
}

.diy-fixedsearch .inner .center {
    height: 28px;
    width: 100%;
    padding: 0 8px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.diy-fixedsearch .inner .center input {
    border-radius: 0;
    background: #fff;
    -webkit-appearance: none;
    height: 28px;
    border: 0;
    width: 100%;
    padding: 0 6px;
    font-size: 14px;
    color: #666;
    float: left;
    line-height: 28px;
    padding-top: 1px;
}

.diy-fixedsearch .inner .center.round input {
    border-radius: 4px;
}

.diy-fixedsearch .inner .center.circle input {
    border-radius: 28px;
}

.follow_topbar ~ .diy-fixedsearch {
    top: 2.6rem;
}

.fui-header ~ .diy-fixedsearch {
    top: 2.2rem;
}

.fui-goods-group .fui-goods-item .salez.diy {
    background-size: 100%;
    background-position: top center;
    z-index: 11;
    top: 10px;
    left: 10px;
    right: 10px;
    width: 5.75rem;
}

.fui-goods-group.block .fui-goods-item .salez.diy {
    background-size: 100%;
    background-position: top center;
    z-index: 11;
    top: 5px;
    left: 5px;
    right: 5px;
    width: auto;
}