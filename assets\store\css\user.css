html {
    font-size: 20px;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    font-family: sans-serif;
    width: 100%;
    height: 100%;
}


body {
    height: 100% !important;
}

@media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
    body {
        padding-top: constant(safe-area-inset-top);
        padding-left: constant(safe-area-inset-left);
        padding-right: constant(safe-area-inset-right);
        padding-bottom: constant(safe-area-inset-bottom);
        padding-top: env(safe-area-inset-top);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .fui-navbar {
        margin-bottom: constant(safe-area-inset-bottom);
        margin-bottom: env(safe-area-inset-bottom);
    }
}

@media only screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) {
    body {
        padding-top: constant(safe-area-inset-top);
        padding-left: constant(safe-area-inset-left);
        padding-right: constant(safe-area-inset-right);
        padding-bottom: constant(safe-area-inset-bottom);
        padding-top: env(safe-area-inset-top);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .fui-navbar {
        margin-bottom: constant(safe-area-inset-bottom);
        margin-bottom: env(safe-area-inset-bottom);
    }
}


@media only screen and (min-width: 400px) {
    html {
        font-size: 21.33333333px !important;
    }
}

@media only screen and (min-width: 414px) {
    html {
        font-size: 22.08px !important;
    }
}

@media only screen and (min-width: 480px) {
    html {
        font-size: 25.6px !important;
    }
}

* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    margin: 0;
    padding: 0
}

body {

    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    font-family: "Helvetica Neue", Helvetica, sans-serif;

    font-size: 0.7rem;
    line-height: 1.5;
    color: #333;
    background: #fafafa;
    overflow: hidden;

}
body {
    width: 100%;
    max-width: 650px;
    margin: auto;
    background: #f3f3f3;
    line-height: 24px;
    font: 14px Helvetica Neue,Helvetica,PingFang SC,Tahoma,Arial,sans-serif;
}
.fui-navbar .label{
    color: unset;
    line-height: 1.8;
}
.account-main{
    height: 100% !important;
}
a {
    text-decoration:none;
}
a:hover{
    text-decoration:none;
}
img.logo{width: 20px;margin: -2px 5px 0 5px;}
.fui-header {
    height: 2.2rem;
    width: 100%;
    box-sizing: border-box;
    font-size: .8rem;
    position: absolute;
    top: 0;
    margin: 0;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    background: #f7f7f7;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
}

.fui-header:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #D9D9D9;
    -webkit-transform-origin: 0 100%;
    -ms-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.fui-header .title {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    height: 2.2rem;
    line-height: 2.2rem;
    color: #000;
    text-align: center;
    position: absolute;
    display: block;
    width: 100%;
    padding: 0;

    top: 0;
    left: 0;
    z-index: 1;
}

.fui-header .fui-header-left {
    position: absolute;
    left: 0;
    padding-left: .3rem;
    height: 2.2rem;
    line-height: 2.2rem;
    z-index: 2;

}

.fui-header .fui-header-right {
    position: absolute;
    right: 0;
    padding-right: .3rem;
    height: 2.2rem;
    line-height: 2.2rem;
    z-index: 2;
    font-size: .7rem
}

.fui-header a.back:before {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 0.5rem;
    width: 0.5rem;
    border-width: 0 0 2px 2px;
    border-color: #666;
    border-style: solid;
    position: relative;
    top: 0;
}

.fui-header a {
    height: 2.2rem;
    line-height: 2.2rem;
    padding: 0;
    margin: 0;
    top: 0;
    color: #999;
    display: block;
    padding: 0 .5rem;
    font-size: .7rem;
}

.fui-header a i {
    font-size: 1.3rem;
}

.fui-header.fui-header-success {
    background-color: #04ab02;
    border: none;
}

.fui-header.fui-header-primary {
    background-color: #0290be;
    border: none;
}

.fui-header.fui-header-warning {
    background-color: #ff8000;
    border: none;
}

.fui-header.fui-header-danger {
    background-color: #ef4f4f;
    border: none;
}

.fui-header.fui-header-success .title,
.fui-header.fui-header-success .btn,
.fui-header.fui-header-primary .title,
.fui-header.fui-header-primary .btn,
.fui-header.fui-header-warning .title,
.fui-header.fui-header-warning .btn,
.fui-header.fui-header-danger .title,
.fui-header.fui-header-danger .btn {
    color: #fff;
}


.fui-content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    min-height: 101%;


}

.fui-content.scroll {
    overflow: hidden;
}

.fui-header ~ .fui-content {
    top: 2.2rem;
}

.fui-header ~ .fui-content.margin {
    top: 2.2rem;
    margin-top: 5px;
}

.fui-tabbar ~ .fui-content {
    margin-top: 2rem;
}

.fui-header ~ .fui-tabbar {
    margin-top: 2.2rem;
}


.fui-navbar ~ .fui-content,
.fui-content.navbar {
    bottom: 2.5rem;
    padding-bottom: 2.5rem;
}

.fui-content.pulldown {
    top: -2.5rem;
}

.fui-content-inner {

    box-sizing: border-box;
    border-top: 1px solid transparent;
    margin-top: -1px;
    padding-bottom: 0.5rem;
}

.fui-content.transitioning,
.fui-content.refreshing,
.fui-content.loading {
    -webkit-transition: -webkit-transform 400ms;
    transition: transform 400ms;
}

.fui-content.refreshing {
    -webkit-transform: translate3d(0, 2.2rem, 0);
    transform: translate3d(0, 2.2rem, 0);
}

.fui-page,
.fui-page-group {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #f4f5fa;
    box-sizing: border-box;
    display: none;
/*    -webkit-overflow-scrolling: touch;*/
}

.fui-page.fui-page-current,
.fui-page-group.fui-page-current,
.fui-page.fui-page-visible,
.fui-page-group.fui-page-visible,
.fui-page.fui-page-from-center-to-left,
.fui-page-group.fui-page-from-center-to-left,
.fui-page.fui-page-from-center-to-right,
.fui-page-group.fui-page-from-center-to-right,
.fui-page.fui-page-from-right-to-center,
.fui-page-group.fui-page-from-right-to-center,
.fui-page.fui-page-from-left-to-center,
.fui-page-group.fui-page-from-left-to-center {
    display: block;
}

.fui-page.fui-page-current,
.fui-page-group.fui-page-current {
    overflow: hidden;
}

.fui-page-group {
    display: block;
}

.fui-page-transitioning,
.fui-page-transitioning .swipeback-page-shadow {
    -webkit-transition: 400ms;
    transition: 400ms;
}

.fui-page-from-right-to-center {
    -webkit-animation: pageFromRightToCenter 400ms forwards;
    animation: pageFromRightToCenter 400ms forwards;
    z-index: 2002;
}

.fui-page-from-center-to-right {
    -webkit-animation: pageFromCenterToRight 400ms forwards;
    animation: pageFromCenterToRight 400ms forwards;
    z-index: 2002;
}
.fui-navbar, .fui-footer {
    position: fixed;
	display: table;
    width: 100%;
    bottom: 0;
    height: 2.45rem;
    background: #fff;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

.option-picker .fui-navbar {
    height: 2.25rem;
}

.fui-navbar .nav-item.btn {
    color: #fff;
    border-radius: 0;
}

.fui-navbar .nav-item {
    position: relative;
    display: table-cell;
    height: 2.45rem;
    text-align: center;
    vertical-align: middle;
    width: 1%;
    color: #999;
}

.page-goods-detail .fui-navbar .nav-item {
    height: 2.45rem;
}

.fui-navbar .nav-item.active,
.fui-navbar .nav-item:active {
    color: #1492fb
}

.fui-navbar .nav-item .label {
    display: block;
    font-size: 0.55rem;
    position: relative;
    top: -0.1rem;
    font-weight: unset !important;
    text-shadow:unset !important;
	white-space: normal;
	padding: 0;
}

.fui-navbar .nav-item .icon {
    font-size: 1rem;
    padding: 0;
}

.fui-navbar .nav-item .badge {
    background: red none repeat scroll 0 0;
    border-radius: 0.5rem;
    color: white;
    font-size: 0.6rem;
    height: 0.8rem;
    left: 50%;
    line-height: 0.8rem;
    margin-left: 0.1rem;
    min-width: 0.8rem;
    padding: 0 0.2rem;
    position: absolute;
    top: 0.1rem;
    vertical-align: top;
    z-index: 100;
}

.fui-footer .btn {
    margin-left: 0;
    margin-top: .3rem;
}

.fui-footer .btn.block {
    margin-left: .4rem;
}