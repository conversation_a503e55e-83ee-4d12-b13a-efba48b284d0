@font-face {
    font-family: 'iconfont';
    src: url('../font/iconfont.eot');
    src: url('../font/iconfont.eot#iefix') format('embedded-opentype'),
    url('../font/iconfont.ttf') format('truetype'),
    url('../font/iconfont.woff') format('woff'),
    url('../font/iconfont.svg#iconfont') format('svg');
    font-weight: normal;
    font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {

    font-family: 'iconfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;


    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-guanbi1:before {
    content: "\e501";
}

.icon-weixin:before {
    content: "\e60c";
}

.icon-zhifubao1:before {
    content: "\e60d";
}

.icon-duigouxuanzhong:before {
    content: "\e618";
}

.icon-xuanzhong4:before {
    content: "\e619";
}

.icon-option_off:before {
    content: "\e62f";
}

.icon-weixin1:before {
    content: "\e63a";
}

.icon-guanbi11:before {
    content: "\e654";
}

.icon-mima:before {
    content: "\e699";
}

.icon-shengchenghaibao:before {
    content: "\e6b8";
}

.icon-suggest:before {
    content: "\e710";
}

.icon-tubiaolunkuo-:before {
    content: "\e617";
}

.icon-guanbi:before {
    content: "\e600";
}