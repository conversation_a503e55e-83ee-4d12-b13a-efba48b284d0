html {
    font-size: 20px;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    font-family: sans-serif;
    width: 100%;
    height: 100%;
}


body {
    height: 100% !important;
}

@media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
    body {
        padding-top: constant(safe-area-inset-top);
        padding-left: constant(safe-area-inset-left);
        padding-right: constant(safe-area-inset-right);
        padding-bottom: constant(safe-area-inset-bottom);
        padding-top: env(safe-area-inset-top);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .fui-navbar {
        margin-bottom: constant(safe-area-inset-bottom);
        margin-bottom: env(safe-area-inset-bottom);
    }
}

@media only screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) {
    body {
        padding-top: constant(safe-area-inset-top);
        padding-left: constant(safe-area-inset-left);
        padding-right: constant(safe-area-inset-right);
        padding-bottom: constant(safe-area-inset-bottom);
        padding-top: env(safe-area-inset-top);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .fui-navbar {
        margin-bottom: constant(safe-area-inset-bottom);
        margin-bottom: env(safe-area-inset-bottom);
    }
}


@media only screen and (min-width: 400px) {
    html {
        font-size: 21.33333333px !important;
    }
}

@media only screen and (min-width: 414px) {
    html {
        font-size: 22.08px !important;
    }
}

@media only screen and (min-width: 480px) {
    html {
        font-size: 25.6px !important;
    }
}

* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    margin: 0;
    padding: 0
}

body {

    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    font-family: "Helvetica Neue", Helvetica, sans-serif;

    font-size: 0.7rem;
    line-height: 1.5;
    color: #333;
    background: #fafafa;
    overflow: hidden;

}

a,
input,
textarea,
select,
button {
    outline: 0 none;
}

a {
    background-color: transparent;
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.pull-right {
    float: right;
}

.pull-left {
    float: left;
}

.text-white {
    color: #fff;
}

.text-default {
    color: #666
}

.text-cancel {
    color: #999
}

.text-success {
    color: #04ab02
}

.text-primary {
    color: #0290be
}

.text-warning {
    color: #ff8000
}

.text-danger {
    color: #ff5555
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-bold {
    font-weight: bold;
}

.btn {
    -moz-appearance: none;
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border-radius: 0.25rem;
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    font-family: inherit;
    font-size: 0.75rem;
    height: 2rem;
    line-height: 1.9rem;
    margin: 0;
    padding: 0 0.6rem;
    position: relative;
    text-align: center;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    border: 1px solid #f90;
    margin: 0.6rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: background-color;
    transition-property: background-color;
    display: inline-block;
}

.btn.fullbtn {
    margin: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    height: 2.25rem;
    line-height: 2.15rem;
    font-size: 0.75rem;
}

.btn.block {
    display: block;
}

.btn.mtop {
    margin-top: 1rem
}

.btn.btn-sm {
    height: 1.1rem;
    line-height: 1rem;
    margin: 0;
    font-size: 0.65rem;
}

.btn.btn-default {
    background: #f7f7f7;
    color: #333;
    border: 1px solid #dfdfdf
}

.btn.btn-default:active {
    background: #dedede;
    color: #a1a1a1;
    border: 1px solid #c8c8c8;
}

.btn.btn-default.disabled {
    background: #f7f7f7;
    color: #b2b2b2;
    border: 1px solid #dfdfdf
}

.option-picker .option-picker-cell.option .btn.btn-default.disabled {
    color: #b2b2b2;
}

.btn.btn-default.disabled:active {
    background: #f7f7f7;
    color: #c9c9c9;
    border: 1px solid #dfdfdf
}

.btn.btn-success {
    background: #04be02;
    color: #fff;
    border: 1px solid #04ab02
}

.btn.btn-success:active,
.btn.btn-success.disabled {
    background: #039702;
    color: #9be59a;
    border: 1px solid #038802;
}

.btn.btn-primary {
    background: #0290be;
    color: #fff;
    border: 1px solid #0281ab
}

.btn.btn-primary:active,
.btn.btn-primary.disabled {
    background: #027297;
    color: #67abc1;
    border: 1px solid #026688;
}

.btn.btn-warning {
    background: #ff8000;
    color: #fff;
    border: 1px solid #ff8000;
}

.btn.btn-warning:active,
.btn.btn-warning.disabled {
    background: #e67300;
    color: #f4a85a;
    border: 1px solid #ce6600;
}

.btn.btn-warning.disabled {
    background: #ccc;
    color: #fff;
    border: 1px solid #ccc;
}

.btn.btn-danger {

    background: #ff5555;
    color: #fff;

    border: 1px solid #ff5555;
}

.btn.btn-blue {
    background: #1492fb;
    color: #fff;
    border: 1px solid #1492fb;
}

.btn.btn-danger:active {
    background: #c13e3e;
    color: #d2848b;
    border: 1px solid #ae3838;
}

.btn.btn-danger.disabled {
    background: #ccc;
    color: #fff;
    border: 1px solid #ccc;
}

.btn.btn-default-o {
    background: transparent;
    color: #666;
    border: 1px solid #666
}

.btn.btn-default-o:active {
    background: transparent;
    color: #666;
    border: 1px solid #666
}

.btn.btn-default-o.disabled {
    background: transparent;
    color: #999;
    border: 1px solid #999;

}

.btn.btn-success-o.disabled,
.btn.btn-primary-o.disabled,
.btn.btn-warning-o.disabled,
.btn.btn-danger-o.disabled {
    background: transparent;
    color: #999;
    border: 1px solid #999
}

.btn.btn-blue-o {
    background: transparent;
    color: #1492fb;
    border: 1px solid #1472fb
}


.btn.btn-success-o {
    background: transparent;
    color: #04be02;
    border: 1px solid #04ab02
}

.btn.btn-primary-o {
    background: transparent;
    color: #0290be;
    border: 1px solid #0290be
}

.btn.btn-warning-o {
    background: transparent;
    color: #ff8000;
    border: 1px solid #ff8000;
}

.btn.btn-danger-o {
    background: transparent;
    color: #ff5555;
    border: 1px solid #ff5555;
}


.fui-page,
.fui-page-group {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #f4f5fa;
    box-sizing: border-box;
    display: none;
    -webkit-overflow-scrolling: touch;
}

.fui-page.fui-page-current,
.fui-page-group.fui-page-current,
.fui-page.fui-page-visible,
.fui-page-group.fui-page-visible,
.fui-page.fui-page-from-center-to-left,
.fui-page-group.fui-page-from-center-to-left,
.fui-page.fui-page-from-center-to-right,
.fui-page-group.fui-page-from-center-to-right,
.fui-page.fui-page-from-right-to-center,
.fui-page-group.fui-page-from-right-to-center,
.fui-page.fui-page-from-left-to-center,
.fui-page-group.fui-page-from-left-to-center {
    display: block;
}

.fui-page.fui-page-current,
.fui-page-group.fui-page-current {
    overflow: hidden;
}

.fui-page-group {
    display: block;
}

.fui-page-transitioning,
.fui-page-transitioning .swipeback-page-shadow {
    -webkit-transition: 400ms;
    transition: 400ms;
}

.fui-page-from-right-to-center {
    -webkit-animation: pageFromRightToCenter 400ms forwards;
    animation: pageFromRightToCenter 400ms forwards;
    z-index: 2002;
}

.fui-page-from-center-to-right {
    -webkit-animation: pageFromCenterToRight 400ms forwards;
    animation: pageFromCenterToRight 400ms forwards;
    z-index: 2002;
}

@-webkit-keyframes pageFromRightToCenter {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: .9;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes pageFromRightToCenter {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: .9;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@-webkit-keyframes pageFromCenterToRight {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
    to {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: .9;
    }
}

@keyframes pageFromCenterToRight {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
    to {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: .9;
    }
}

.fui-page-from-center-to-left {
    -webkit-animation: pageFromCenterToLeft 400ms forwards;
    animation: pageFromCenterToLeft 400ms forwards;
}

.fui-page-from-left-to-center {
    -webkit-animation: pageFromLeftToCenter 400ms forwards;
    animation: pageFromLeftToCenter 400ms forwards;
}

@-webkit-keyframes pageFromCenterToLeft {
    from {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    to {
        opacity: 0.5;
        -webkit-transform: translate3d(-20%, 0, 0);
        transform: translate3d(-20%, 0, 0);
    }
}

@keyframes pageFromCenterToLeft {
    from {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    to {
        opacity: 0.5;
        -webkit-transform: translate3d(-20%, 0, 0);
        transform: translate3d(-20%, 0, 0);
    }
}

@-webkit-keyframes pageFromLeftToCenter {
    from {
        opacity: .5;
        -webkit-transform: translate3d(-20%, 0, 0);
        transform: translate3d(-20%, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes pageFromLeftToCenter {
    from {
        opacity: .5;
        -webkit-transform: translate3d(-20%, 0, 0);
        transform: translate3d(-20%, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}


.fui-header {
    height: 2.2rem;
    width: 100%;
    box-sizing: border-box;
    font-size: .8rem;
    position: absolute;
    top: 0;
    margin: 0;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    background: #f7f7f7;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
}

.fui-header:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #D9D9D9;
    -webkit-transform-origin: 0 100%;
    -ms-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.fui-header .title {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    height: 2.2rem;
    line-height: 2.2rem;
    color: #000;
    text-align: center;
    position: absolute;
    display: block;
    width: 100%;
    padding: 0;

    top: 0;
    left: 0;
    z-index: 1;
}

.fui-header .fui-header-left {
    position: absolute;
    left: 0;
    padding-left: .3rem;
    height: 2.2rem;
    line-height: 2.2rem;
    z-index: 2;

}

.fui-header .fui-header-right {
    position: absolute;
    right: 0;
    padding-right: .3rem;
    height: 2.2rem;
    line-height: 2.2rem;
    z-index: 2;
    font-size: .7rem
}

.fui-header a.back:before {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 0.5rem;
    width: 0.5rem;
    border-width: 0 0 2px 2px;
    border-color: #666;
    border-style: solid;
    position: relative;
    top: 0;
}

.fui-header a {
    height: 2.2rem;
    line-height: 2.2rem;
    padding: 0;
    margin: 0;
    top: 0;
    color: #999;
    display: block;
    padding: 0 .5rem;
    font-size: .7rem;
}

.fui-header a i {
    font-size: 1.3rem;
}

.fui-header.fui-header-success {
    background-color: #04ab02;
    border: none;
}

.fui-header.fui-header-primary {
    background-color: #0290be;
    border: none;
}

.fui-header.fui-header-warning {
    background-color: #ff8000;
    border: none;
}

.fui-header.fui-header-danger {
    background-color: #ef4f4f;
    border: none;
}

.fui-header.fui-header-success .title,
.fui-header.fui-header-success .btn,
.fui-header.fui-header-primary .title,
.fui-header.fui-header-primary .btn,
.fui-header.fui-header-warning .title,
.fui-header.fui-header-warning .btn,
.fui-header.fui-header-danger .title,
.fui-header.fui-header-danger .btn {
    color: #fff;
}


.fui-content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    min-height: 101%;


}

.fui-content.scroll {
    overflow: hidden;
}

.fui-header ~ .fui-content {
    top: 2.2rem;
}

.fui-header ~ .fui-content.margin {
    top: 2.2rem;
    margin-top: 5px;
}

.fui-tabbar ~ .fui-content {
    margin-top: 2rem;
}

.fui-header ~ .fui-tabbar {
    margin-top: 2.2rem;
}


.fui-navbar ~ .fui-content,
.fui-content.navbar {
    bottom: 2.5rem;
    padding-bottom: 2.5rem;
}

.fui-content.pulldown {
    top: -2.5rem;
}

.fui-content-inner {

    box-sizing: border-box;
    border-top: 1px solid transparent;
    margin-top: -1px;
    padding-bottom: 0.5rem;
}

.fui-content.transitioning,
.fui-content.refreshing,
.fui-content.loading {
    -webkit-transition: -webkit-transform 400ms;
    transition: transform 400ms;
}

.fui-content.refreshing {
    -webkit-transform: translate3d(0, 2.2rem, 0);
    transform: translate3d(0, 2.2rem, 0);
}

.fui-content.loading {

}

.fui-btn-group {
    -moz-appearance: none;
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border-radius: 0.25rem;
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    font-family: inherit;
    font-size: 0.7rem;
    height: 1.8rem;
    line-height: 1.7rem;
    margin: 0;
    position: relative;
    text-align: center;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0.5em;
    -webkit-user-select: none;
    -moz-user-select: none;
}

.fui-btn-group.fui-btn-group-default {
    border: 1px solid #d9d9d9;
}

.fui-btn-group.fui-btn-group-success {
    border: 1px solid #04ab02;
}

.fui-btn-group.fui-btn-group-primary {
    border: 1px solid #0290be;
}

.fui-btn-group.fui-btn-group-warning {
    border: 1px solid #ff8000;
}

.fui-btn-group.fui-btn-group-danger {
    border: 1px solid #ef4f4f;
}

.fui-btn-group.fui-btn-group-default a {
    color: #aaa;
}

.fui-btn-group.fui-btn-group-success a {
    color: #04ab02;
}

.fui-btn-group.fui-btn-group-primary a {
    color: #0290be;
}

.fui-btn-group.fui-btn-group-warning a {
    color: #ff8000;
}

.fui-btn-group.fui-btn-group-danger a {
    color: #ef4f4f;
}

.fui-btn-group a {
    display: table-cell;
    width: 1%;
    color: #333;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: background-color;
    transition-property: background-color;
}

.fui-btn-group a.active,
.fui-btn-group a:active {
    color: #fff;
}

.fui-btn-group.fui-btn-group-default a.active,
.fui-btn-group.fui-btn-group-default a:active {
    background: #ccc;
    color: #fff;
}

.fui-btn-group.fui-btn-group-success a.active,
.fui-btn-group.fui-btn-group-success a:active {
    background: #04ab02;
}

.fui-btn-group.fui-btn-group-primary a.active,
.fui-btn-group.fui-btn-group-primary a:active {
    background: #0290be;
}

.fui-btn-group.fui-btn-group-warning a.active,
.fui-btn-group.fui-btn-group-warning a:active {
    background: #ff8000;
}

.fui-btn-group.fui-btn-group-danger a.active,
.fui-btn-group.fui-btn-group-danger a:active {
    background: #ef4f4f;
}

.badge {
    background-color: #ff5555;
    border-radius: 5rem;
    color: #fff;
    display: inline-block;
    font-size: 0.6rem;
    line-height: 1;
    padding: 0.15rem 0.45rem 0.15rem;
}

.badge.badge-success {
    background: #04ab02;
    color: #fff;
}

.badge.badge-primary {
    background: #0290be;
    color: #fff;
}

.badge.badge-danger {

    background: #ff5555;
    color: #fff;
}

.badge.badge-warning {
    background: #ff8000;
    color: #fff;
}

.fui-navbar, .fui-footer {
    position: fixed;
	display: table;
    width: 100%;
    bottom: 0;
    height: 2.45rem;
    background: #fff;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

.option-picker .fui-navbar {
    height: 2.25rem;
}

.fui-navbar .nav-item.btn {
    color: #fff;
    border-radius: 0;
}

.fui-navbar .nav-item {
    position: relative;
    display: table-cell;
    height: 2.45rem;
    text-align: center;
    vertical-align: middle;
    width: 1%;
    color: #999;
}

.page-goods-detail .fui-navbar .nav-item {
    height: 2.45rem;
}

.fui-navbar .nav-item.active,
.fui-navbar .nav-item:active {
    color: red
}

.fui-navbar .nav-item .label {
    display: block;
    font-size: 0.55rem;
    position: relative;
    top: -0.1rem;
	white-space: normal;
	padding: 0;
}

.fui-navbar .nav-item .icon {
    font-size: 1rem;
    padding: 0;
}

.fui-navbar .nav-item .badge {
    background: red none repeat scroll 0 0;
    border-radius: 0.5rem;
    color: white;
    font-size: 0.6rem;
    height: 0.8rem;
    left: 50%;
    line-height: 0.8rem;
    margin-left: 0.1rem;
    min-width: 0.8rem;
    padding: 0 0.2rem;
    position: absolute;
    top: 0.1rem;
    vertical-align: top;
    z-index: 100;
}

.fui-footer .btn {
    margin-left: 0;
    margin-top: .3rem;
}

.fui-footer .btn.block {
    margin-left: .4rem;
}

.fui-title {
    color: #888;
    font-size: .7rem;
    margin: 0.5rem 0;
    padding: 0 0.7rem;
}

.fui-label {
    display: inline-block;
    padding: 0 .2rem;
    background: #d9d9d9;
    color: #333;
    margin: 0 .1rem;
    font-size: 0.6rem;
}

.fui-label.fui-label-success {

}

.fui-label.fui-label-primary {

}

.fui-label.fui-label-warning {

}

.fui-label.fui-label-danger {

}

.fui-label.fui-label-safety {

}

.fui-label.fui-label-success {
    background: #04ab02;
    color: #fff;
}

.fui-label.fui-label-primary {
    background: #0290be;
    color: #fff;
}

.fui-label.fui-label-warning {
    background: #ff8000;
    color: #fff;
}

.fui-label.fui-label-danger {
    background: #ff5555;
    color: #fff;
}

.fui-label.fui-label-safety {
    background: #07B40A;
    color: #fff;
}

.fui-cell-group {
    margin-top: 0.5rem;
    background-color: #fff;
    line-height: 1.4;
    font-size: 0.8rem;
    overflow: hidden;
    position: relative;
    display: block;
}

.fui-cell-group.transparent {
    background: transparent;
}

.page-goods-detail .fui-sale-group.fui-cell-group:before {
    content: " ";
    position: absolute;
    left: 0.5rem;
    right: 0.5rem;
    top: 0;
    height: 1px;
    border-top: 1px solid #ebebeb;
    color: #ebebeb;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.fui-cell-group.notop {
    margin-top: 0;
}

.fui-cell-group.sm * {
    font-size: 0.7rem;
}

.fui-cell-group.fui-cell-group-o {
    margin: 0 .5rem;
    margin-top: 0.5rem;
    border-radius: .5rem;
}

.fui-cell-group.fui-cell-group-o:active a:last-child {
    border-bottom-left-radius: 0.3rem;
    border-bottom-right-radius: 0.3rem;
}

.fui-cell-group.fui-cell-group-o:active a:first-child {
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
}

.fui-cell-group .fui-cell {
    position: relative;
    padding: 0.75rem 0.6rem 0.65rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: background-color;
    transition-property: background-color;
    line-height: 1.2;
}

.fui-cell-group .fui-cell.fui-sale-cell {
    padding: 0.45rem 0.6rem 0.45rem;
}

.fui-cell-group .fui-cell.changepwd, .fui-cell-group .fui-cell.btn-logout {
    padding: 0;
}

.fui-cell-group .fui-cell.changepwd .fui-cell-text {
    border: 1px solid #ff5555;
    -webkit-border-radius: 0.25rem;
    -moz-border-radius: 0.25rem;
    border-radius: 0.25rem;
    margin: 0.5rem 0.6rem;
    color: #ff5555;
    background: #fff;
    height: 2rem;
    line-height: 2rem;
}

.fui-cell-group .fui-cell.btn-logout .fui-cell-text {
    -webkit-border-radius: 0.25rem;
    -moz-border-radius: 0.25rem;
    border-radius: 0.25rem;
    margin: 0rem 0.6rem;
    color: #fff;
    background: #ff5555;
    height: 2rem;
    line-height: 2rem;
}

.fui-cell-group .fui-cell.btn-logout .fui-cell-text {
    color: #ffffff;
}

.fui-cell-group .fui-cell:before {
    content: " ";
    position: absolute;
    left: 0.5rem;
    right: 0.5rem;
    bottom: -1px;
    height: 1px;
    border-top: 1px solid #ebebeb;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.fui-cell-group .fui-cell.must .fui-cell-label:after {
    content: "*";
    position: absolute;
    right: 0.2rem;
    top: 0.2rem;
    color: #f30;
    font-size: 0.8rem
}

.fui-cell-group-o:after,
.fui-cell-group .fui-cell:last-child:before {
    display: none;
}


.fui-cell-group.fui-cell-click .fui-cell:active,
.fui-cell-group .fui-cell.fui-cell-click:active {
    background: #ececec;
}

.fui-cell-title {
    color: #333;
    font-size: 0.75rem;
    padding: 0.5rem 0.7rem;
}

.fui-cell-tip {
    color: #999;
    font-size: 0.6rem;

    padding: 0.3rem 0.7rem 0;
}

.fui-cell-group .fui-cell .fui-cell-info {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    font-size: .7rem;
    color: #000;
}

.fui-cell-group .fui-cell .fui-cell-info.md {
    font-size: 0.65rem;
}

.fui-cell-group .fui-cell .fui-cell-info.overflow {
    word-break: break-all;
    overflow: auto;
    white-space: normal;
    height: auto;
}

.fui-cell-group .fui-cell .fui-cell-info .checkbox-inline,
.fui-cell-group .fui-cell .fui-cell-info .radio-inline {
    float: left;
    margin-bottom: .2rem;
}

.fui-cell-group .fui-cell .fui-cell-label {
    position: relative;
    display: block;
    width: 3.85rem;

    font-size: 0.7rem;
    color: #666;
}

.param-block .fui-cell-group .fui-cell .fui-cell-label {
    color: #999;
}

.fui-cell-group .fui-cell .fui-cell-label.md {
    font-size: 0.65rem;
}

.fui-cell-group .fui-cell .fui-cell-label.big {
    display: block;
    width: 4.5rem;
    padding: 0 0.2rem;
    font-size: 0.8rem;
}


.fui-cell-group .fui-cell .fui-input {

    width: 100%;
    border: 0;
    outline: 0;
    -webkit-appearance: none;
    background-color: transparent;
    font-size: inherit;
    color: inherit;
    height: 1.41176471em;
    line-height: 1.41176471;
}

.fui-cell-group .fui-cell .fui-cell-info ::-webkit-input-placeholder {
    color: #b2b2b2
}

.fui-cell-group .fui-cell .fui-input::-webkit-outer-spin-button,
.fui-cell-group .fui-cell .fui-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.fui-cell-group .fui-cell .fui-cell-info select {
    -webkit-appearance: none;
    border: 0;
    outline: 0;
    background-color: transparent;
    width: 100%;
    font-size: inherit;
    height: 1.2rem;
    position: relative;
    z-index: 1;
    padding-left: 15px;
}

.fui-cell-group .fui-cell .fui-cell-info select {
    padding: 0;
}

.fui-cell-group .fui-cell .fui-cell-icon {
    color: #333;
    margin-right: 0.3rem;
    width: 0.8rem;
    text-align: center;
    line-height: 0.7rem;
}

.fui-cell-group .fui-cell .fui-cell-icon i {
    font-size: 16px;
    color: #666;

}

.fui-cell-group .fui-cell .fui-cell-icon.pull-left {
    font-size: 0.85rem;
}

.fui-cell-group .fui-cell .fui-cell-text {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #000;
    font-size: 0.7rem;
    line-height: 0.7rem;
}

.fui-cell-group .fui-cell .fui-cell-new {
    background: #d81010;
    color: white;
    padding: 0 0.25rem;
    font-size: 0.6rem;
    border-radius: 4px;
    margin-right: 0.5rem;
}

.fui-cell-group .fui-cell .fui-cell-text .coupon-mini {
    font-size: 0.6rem;
    line-height: 0.7rem;
}

.fui-cell-group .fui-cell .fui-cell-remark {
    color: #999;
    text-align: right;
    font-size: 0.65rem;
    margin-right: .2rem;
    line-height: 0.65rem;
}

.fui-cell-group .fui-cell .fui-cell-remark:after {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 0.4rem;
    width: 0.4rem;
    border-width: 1px 1px 0 0;
    border-color: #C8C8CD;
    border-style: solid;
    position: relative;
    top: 0px;
    margin-left: 0;
}

.fui-cell-group .fui-cell .fui-cell-remark.noremark:after {
    display: none;
    margin-left: 0;
}

.fui-cell-group .fui-cell.fui-cell-textarea .fui-cell-label {
    margin-top: -1.5rem;
}

.fui-cell-group .fui-cell textarea {
    width: 100%;
    background: transparent;
    border: none;
    resize: none;
    font-size: 0.7rem;
    font-family: "Helvetica Neue", Helvetica, sans-serif;
}

.fui-cell-group .fui-cell .textarea_counter {
    font-size: 0.7rem;
    color: #666;
    text-align: right;
}

.fui-cell-group .fui-cell.warning {
    color: #e64340;
}

.fui-switch {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    position: relative;
    width: 47px;
    height: 27px;
    border: 1px solid #DFDFDF;
    outline: 0;
    border-radius: 16px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    background: #DFDFDF;
}

.fui-switch:before {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 45px;
    height: 25px;
    border-radius: 15px;
    background-color: #FDFDFD;
    -webkit-transition: -webkit-transform .3s;
    transition: transform .3s;
}

.fui-switch:after {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 25px;
    height: 25px;
    border-radius: 15px;
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    -moz-transition: transform .3s;
    -webkit-transition: -webkit-transform .3s;
    transition: transform .3s;
}

.fui-switch.fui-switch-small {
    width: 40px;
    height: 22px;
}

.fui-switch.fui-switch-small:before {
    width: 38px;
    height: 20px;
    border-radius: 15px;
}

.fui-switch.fui-switch-small:after {
    width: 20px;
    height: 20px;
    border-radius: 15px;
}

.fui-switch.fui-switch-small:checked:before {

}

.fui-switch.fui-switch-small:checked:after {
    -webkit-transform: translateX(18px);
    -ms-transform: translateX(18px);
    transform: translateX(18px);
}

.fui-switch.fui-switch-small:checked:before {

}

.fui-switch.fui-switch-small:checked:after {
    -webkit-transform: translateX(18px);
    -ms-transform: translateX(18px);
    transform: translateX(18px);
}

.fui-switch.fui-switch-default:checked {
    border-color: #ccc;
    background-color: #ccc;
}

.fui-switch.fui-switch-success:checked {
    border-color: #04BE02;
    background-color: #04BE02;
}

.fui-switch.fui-switch-primary:checked {
    border-color: #0290be;
    background-color: #0290be;
}

.fui-switch.fui-switch-warning:checked {
    border-color: #ff8000;
    background-color: #ff8000;
}

.fui-switch.fui-switch-danger:checked {
    border-color: #ff5555;
    background-color: #ff5555;
}

.fui-switch:checked:before {
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
}

.fui-switch:checked:after {
    -webkit-transform: translateX(20px);
    -ms-transform: translateX(20px);
    transform: translateX(20px);
}

.fui-images {
    list-style: none;
    padding-top: .2rem;
}

.fui-images .image {
    float: left;
    margin-right: 0.3rem;
    margin-bottom: 0.3rem;
    width: 4rem;
    height: 4rem;
    background: no-repeat center center;
    background-size: cover;
    position: relative;

}

.fui-images .image.long {
    width: 8rem;
}

.fui-images .image .image-remove {
    position: absolute;
    right: 0;
    top: 0;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    background: #000 \9;
    filter: alpha(opacity=50);
    width: .75rem;
    height: .75rem;
    text-align: center;
    border-bottom-left-radius: 0.25rem;
    z-index: 9999;
}

.fui-images .image .image-remove i {
    font-size: .6rem;
}

.fui-images.fui-images-sm .image.image-sm {
    width: 2rem;
    height: 2rem;
}

.fui-images.fui-images-sm .image.image-sm.long {
    width: 3rem;
}

.fui-uploader {
    float: left;
    position: relative;
    margin-right: 0.3rem;
    margin-bottom: 0.3rem;
    width: 4rem;
    height: 4rem;
    background: #f5f5f5;
}

.fui-uploader.long {
    width: 8rem;
}

.fui-uploader:before,
.fui-uploader:after {
    content: " ";
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #D9D9D9;
}

.fui-uploader:before {
    width: 2px;
    height: 2rem;
}

.fui-uploader:after {
    width: 2rem;
    height: 2px;
}

.fui-uploader.fui-uploader-sm {
    width: 2rem;
    height: 2rem;
}

.fui-uploader.fui-uploader-sm:before {
    height: 1rem;
}

.fui-uploader.fui-uploader-sm:after {
    width: 1rem;
}

.fui-uploader:active {
    border-color: #999999;
}

.fui-uploader:active:before,
.fui-uploader:active:after {
    background-color: #999999;
}

.fui-uploader input {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
}

.fui-mask,
.fui-mask-m {
    position: fixed;
    background: rgba(0, 0, 0, 0.8) none repeat scroll 0 0;
    width: 100%;
    height: 100%;
    left: 0;
    opacity: 0;
    top: 0;
    z-index: 1000;
    -moz-transition-duration: 400ms;
    -webkit-transition-duration: 400ms;
    transition-duration: 400ms;
}

.fui-mask-transparent .visible,
.fui-mask.visible,
.fui-mask-m.visible {
    opacity: 1;
}

.fui-mask.fui-mask-transparent {
    background: none;
    opacity: 0;
}

.fui-preloader {
    display: inline-block;
    text-align: center;
    width: 1rem;
    height: 1rem;
    -webkit-transform-origin: 50%;
    transform-origin: 50%;
    -webkit-animation: preloader-spin 1s steps(12, end) infinite;
    animation: preloader-spin 1s steps(12, end) infinite;
}

.fui-preloader:after {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%236c6c6c'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
    background-position: 50%;
    background-size: 100%;
    background-repeat: no-repeat;
}

.fui-preloader.fui-preloader-white:after {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%23fff'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}

@-webkit-keyframes preloader-spin {
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes preloader-spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.fui-loader {
    position: fixed;
    left: 50%;
    top: 50%;
    padding: 0.4rem;
    margin-left: -1.25rem;
    margin-top: -1.25rem;
    background: rgba(0, 0, 0, 0.7);
    z-index: 11000;
    border-radius: 0.25rem;
}

.fui-loader.fui-loader-tip {
    width: 7rem;
    height: 7rem;
    margin-left: -3.5rem;
    margin-top: -5rem;
    text-align: center;
}

.fui-loader-tip .fui-preloader {
    position: absolute;
    left: 50%;
    margin-left: -1rem;
    margin-top: 1rem;
    width: 2.2rem;
    height: 2.2rem;
}

.fui-loader-tip .fui-loader-text {
    color: #fafafa;
    font-size: 0.7rem;
    position: absolute;
    text-align: center;
    left: 0;
    bottom: 1.5rem;
    width: 100%;
}

.fui-loader-tip .fui-loader-icon {
    color: #fff;
}

.fui-loader-tip .fui-loader-icon i {
    font-size: 3rem;
    margin-top: 0.5rem;
}

.fui-loader .fui-preloader {
    display: block;
    width: 1.8rem;
    height: 1.8rem;
}

.fui-toast {
    position: fixed;
    left: 0;
    text-align: center;
    width: 100%;
    opacity: 0;
    -webkit-transition-property: opacity;
    transition-property: transform, opacity;
    -webkit-transform: translate3d(0, 0, 0) scale(.9);
    transform: translate3d(0, 0, 0) scale(.9);
    -webkit-transition-property: -webkit-transform, opacity;
    transition-property: transform, opacity;
    z-index: 10001;
}

.fui-toast.in {
    opacity: 1;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 0, 0) scale(1);
    transform: translate3d(0, 0, 0) scale(1);
}

.fui-toast.out {
    opacity: 0;
    z-index: 10999;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 0, 0) scale(0.8);
    transform: translate3d(0, 0, 0) scale(0.8);
}

.fui-toast .fui-toast-text {
    display: inline-block;
    color: #fff;
    padding: 0.4rem 0.5rem;
    border-radius: 0.2rem;
    background: rgba(0, 0, 0, 0.75);
    font-size: 0.75rem;
    line-height: 1rem;
    max-width: 60%;
}

.fui-notify {
    width: 100%;
    padding: 0.5rem;
    overflow: hidden;
}

.fui-notify.fui-notify-default {
    background: rgba(0, 0, 0, 0.7);
}

.fui-notify.fui-notify-success {
    background: rgba(3, 151, 2, 0.9);
}

.fui-notify.fui-notify-warning {
    background: rgba(230, 110, 0, 0.9);
}

.fui-notify.fui-notify-danger {
    background: rgba(174, 56, 56, 0.9);
}

.fui-notify.fui-notify-primary {
    background: rgba(2, 114, 151, 0.9);
}

.fui-notify .fui-notify-close {
    position: absolute;
    right: 0.3rem;
    top: 0.3rem;
    color: #fff;
}

.fui-notify .fui-notify-title,
.fui-notify .fui-notify-text {
    color: #fff;
    width: 100%;
    float: left;
    text-align: left;
    font-size: 0.8rem;
    font-weight: bold;
}

.fui-notify .fui-notify-text {
    font-size: 0.65rem;
    font-weight: normal;
}

.fui-modal.dialog-modal {

    position: absolute;
    z-index: 1001;
    left: 50%;
    margin-left: -6.75rem;
    margin-top: 0;
    top: 50%;
    width: 13.5rem;
    opacity: 0;
    -webkit-transform: translate3d(0, 0, 0) scale(1.2);
    transform: translate3d(0, 0, 0) scale(1.2);
    -webkit-transition-property: -webkit-transform, opacity;
    transition-property: transform, opacity;
    height: auto;
}

.fui-modal.dialog-modal.in {
    opacity: 1;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 0, 0) scale(1);
    transform: translate3d(0, 0, 0) scale(1);
}

.fui-modal.dialog-modal.out {
    opacity: 0;
    z-index: 10999;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 0, 0) scale(0.8);
    transform: translate3d(0, 0, 0) scale(0.8);
}

.fui-dialog {
    background: #fff;
    text-align: center;
    border-radius: 0.35rem;
    padding-top: 0.3rem;
    overflow: hidden;
}


.fui-dialog .fui-dialog-title {
    padding: .2rem .5rem;
    font-weight: 400;
    font-size: 0.8rem;
}

.fui-dialog .fui-dialog-text {
    padding: .4rem .8rem;
    font-size: 0.75rem;
    color: #888;
}

.fui-dialog .fui-dialog-prompt {
    width: 100%;
    padding: .2rem .5rem;
}

.fui-dialog .fui-dialog-prompt input {
    width: 100%;
    height: 1.6rem;
    padding: 0 0.5rem;
    font-size: 0.7rem;
    color: #333;
    -webkit-appearance: none;
    border: 1px solid #d9d9d9;
    color: #333;
}

.fui-dialog .fui-dialog-btns {
    position: relative;
    line-height: 2rem;
    margin-top: .3rem;
    font-size: 0.8rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.fui-dialog .fui-dialog-btns a {
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #666;
    text-decoration: none;
}

.fui-dialog .fui-dialog-btns a:active {
    background-color: #EEEEEE;
}

.fui-dialog .fui-dialog-btns:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #D5D5D6;
    color: #D5D5D6;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.fui-dialog-confirm .fui-dialog-text {
    text-align: left;
}

.fui-dialog-confirm .fui-dialog-btns a {
    position: relative;
}

.fui-dialog-confirm .fui-dialog-btns a:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    border-left: 1px solid #D5D5D6;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(0.5);
    -ms-transform: scaleX(0.5);
    transform: scaleX(0.5);
}

.fui-dialog-confirm .fui-dialog-btns a:first-child:after {
    display: none;
}

.fui-dialog-confirm .fui-dialog-btns a.cancel {
    color: #666;
}

.fui-dialog-confirm .fui-dialog-btns a.confirm {
    color: #000;
}

.fui-tab-o,
.fui-tab {
    -webkit-align-self: center;
    align-self: center;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-lines: single;
    -moz-box-lines: single;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
    margin-bottom: .5rem;
}

.fui-tab-plus {
    -webkit-align-self: center;
    align-self: center;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-lines: single;
    -moz-box-lines: single;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
}

.fui-tab a,
.fui-tab-o a {
    text-align: center;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;

    -webkit-transition-property: color;
    transition-property: color;


}

.fui-tab-plus a {
    text-align: center;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    position: relative;
    -webkit-transition-property: color;
    transition-property: color;


}

.fui-tab-o a {
    border-radius: 0 0 0 0;
    margin-left: -1px;
    width: 100%;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    color: #666;
    font-size: 0.75rem;
    padding: 0.25rem;
}

.fui-tab a.active {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;

    -webkit-transition-property: border-color;
    transition-property: border-color;
}

.fui-tab-o a:first-child {
    border-radius: 0.25rem 0 0 0.25rem;
    margin-left: 0;
    border-left-width: 1px;
    border-left-style: solid;
}

.fui-tab-o a:last-child {
    border-radius: 0 0.25rem 0.25rem 0;
}

.fui-tab {
    background: white;
    position: relative;
}

.fui-tab-plus {
    background: white;
    position: relative;
}

.fui-tab:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    height: 1px;
    width: 100%;
    background-color: #d0d0d0;
    display: block;
    z-index: 15;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}

.fui-tab-plus a:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    height: 1px;
    width: 100%;
    background-color: #d0d0d0;
    display: block;
    z-index: 15;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}

.fui-tab-plus a:before {
    content: '';
    position: absolute;
    left: -1px;
    bottom: 0;
    right: auto;
    top: auto;
    height: 100%;
    width: 1px;
    background-color: #d0d0d0;
    display: block;
    z-index: 15;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}

.fui-tab-plus a.active:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    height: 1px;
    width: 100%;
    background-color: #fff;
    display: block;
    z-index: 20;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}

.fui-tab-plus a:first-child:before {
    background: #fff;
}


@media only screen and (-webkit-min-device-pixel-ratio: 2) {
    .fui-tab:after {
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }

    .fui-tab-plus:after {
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
    .fui-tab:after {
        -webkit-transform: scaleY(0.33);
        transform: scaleY(0.33);
    }

    .fui-tab-plus:after {
        -webkit-transform: scaleY(0.33);
        transform: scaleY(0.33);
    }
}

.fui-tab a {
    color: #666;
    font-size: 0.7rem;
    width: 100%;
    height: 2rem;
    line-height: 2rem;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    border: 0;
    border-bottom: 2px solid transparent;
    border-radius: 0;
}

.fui-tab-plus a {
    color: #666;
    font-size: 0.8rem;
    width: 100%;
    height: 2rem;
    line-height: 2rem;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    border: 0;
    border-top: 2px solid transparent;
    border-radius: 0;
}

.fui-tab-plus.fui-tab-plus-default a.active {
    color: #666;
    border-color: #666;
    z-index: 15;
}

.fui-tab-plus.fui-tab-plus-success a.active {
    color: #04be02;
    border-color: #04ab02;
    z-index: 15;
}

.fui-tab-plus.fui-tab-plus-primary a.active {
    color: #0290be;
    border-color: #0290be;
    z-index: 100;
}

.fui-tab-plus.fui-tab-plus-warning a.active {
    color: #ff8000;
    border-color: #ff8000;
    z-index: 100;
}

.fui-tab-plus.fui-tab-plus-danger a.active {
    color: #ef4f4f;
    border-color: #ef4f4f;
    z-index: 100;
}

.fui-tab.fui-tab-default a.active {
    color: #666;
    border-color: #666;
    z-index: 100;
}

.fui-tab.fui-tab-success a.active {
    color: #04be02;
    border-color: #04ab02;
    z-index: 100;
}

.fui-tab.fui-tab-primary a.active {
    color: #0290be;
    border-color: #0290be;
    z-index: 100;
}

.fui-tab.fui-tab-warning a.active {
    color: #ff8000;
    border-color: #ff8000;
    z-index: 100;
}

.fui-tab.fui-tab-danger a.active {

    color: #ff5555;
    border-color: #ff5555;
    z-index: 100;
}

.fui-tab-o.fui-tab-default a {
    color: #333
}

.fui-tab-o.fui-tab-default a,
.fui-tab-o.fui-tab-default a.active {
    border: 1px solid #999;
}

.fui-tab-o.fui-tab-default a.active {
    background: #999;
}

.fui-tab-o.fui-tab-success a {
    color: #04ab02;
}

.fui-tab-o.fui-tab-success a,
.fui-tab-o.fui-tab-success a.active {
    border: 1px solid #04ab02;
}

.fui-tab-o.fui-tab-success a.active {
    background: #04ab02;
}

.fui-tab-o.fui-tab-primary a {
    color: #0290be
}

.fui-tab-o.fui-tab-primary a,
.fui-tab-o.fui-tab-primary a.active {
    border: 1px solid #0290be;
}

.fui-tab-o.fui-tab-primary a.active {
    background: #0290be;
}

.fui-tab-o.fui-tab-warning a {
    color: #ff8000;
}

.fui-tab-o.fui-tab-warning a,
.fui-tab-o.fui-tab-warning a.active {
    border: 1px solid #ff8000;
}

.fui-tab-o.fui-tab-warning a.active {
    background: #ff8000;
}

.fui-tab-o.fui-tab-danger a {
    color: #ef4f4f;
}

.fui-tab-o.fui-tab-danger a,
.fui-tab-o.fui-tab-danger a.active {
    border: 1px solid #ef4f4f;
}

.fui-tab-o.fui-tab-danger a.active {
    background: #ef4f4f;
}

.fui-tab-o a.active {
    color: white;
    z-index: 90;
}

.fui-tab-o {
    margin-left: 1rem;
    margin-right: 1rem;
}

.fui-tab-content {
    background: #fff;
}

.row {
    overflow: hidden;
}

.row > [class*="col-"] {
    box-sizing: border-box;
    float: left;
}

.row .col-100 {
    width: 96%;
    margin-left: 4%;
}

.row .col-90 {
    width: 86%;
    margin-left: 4%;
}

.row .col-95 {
    width: 91%;
    margin-left: 4%;
}

.row .col-80 {
    width: 76%;
    margin-left: 4%;
}

.row .col-85 {
    width: 81%;
    margin-left: 4%;
}

.row .col-70 {
    width: 66%;
    margin-left: 4%;
}

.row .col-75 {
    width: 71%;
    margin-left: 4%;
}

.row .col-60 {
    width: 56%;
    margin-left: 4%;
}

.row .col-65 {
    width: 61%;
    margin-left: 4%;
}

.row .col-55 {
    width: 51%;
    margin-left: 4%;
}

.row .col-50 {
    width: 46%;
    margin-left: 4%;
}

.row .col-45 {
    width: 41%;
    margin-left: 4%;
}

.row .col-40 {
    width: 36%;
    margin-left: 4%;
}

.row .col-30 {
    width: 26%;
    margin-left: 4%;
}

.row .col-35 {
    width: 31%;
    margin-left: 4%;
}

.row .col-20 {
    width: 16%;
    margin-left: 4%;
}

.row .col-25 {
    width: 21%;
    margin-left: 4%;
}

.row .col-15 {
    width: 11%;
    margin-left: 4%;
}

.row .col-10 {
    width: 6%;
    margin-left: 4%;
}

.row .col-5 {
    width: 1%;
    margin-left: 4%;
}

.fui-searchbar {
    position: relative;
    z-index: 10;
    height: 2.2rem;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
    background-color: #f7f7f8;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.fui-searchbar:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    height: 1px;
    width: 100%;
    background-color: #e7e7e7;
    display: block;
    z-index: 15;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
    .fui-searchbar:after {
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
    .fui-searchbar:after {
        -webkit-transform: scaleY(0.33);
        transform: scaleY(0.33);
    }
}

.fui-searchbar .searchbar {
    margin: 0 -0.5rem;

    padding: 0.4rem 0.6rem;
    background: #e8e8e8;
}

.fui-searchbar .searchbar .search-input input {
    border: 0;
}

.fui-searchbar .searchbar .searchbar-cancel {
    color: #5f646e;
}

.searchbar {
    padding: 0.5rem 0;
    overflow: hidden;
    height: 2.2rem;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    position: relative;
}

.searchbar .searchbar-cancel {
    position: absolute;
    top: 0.4rem;
    right: -2rem;
    width: 1.8rem;
    float: right;
    height: 1.4rem;
    line-height: 1.4rem;
    text-align: center;
    -webkit-transition: all .3s;
    transition: all .3s;
    opacity: 0;
    -webkit-transform: translate3d(0, 50, 0);
    transform: translate3d(0, 50, 0);
    font-size: 0.7rem;
}

.searchbar .search-input {
    -webkit-transform: translate3d(0, 50, 0);
    transform: translate3d(0, 50, 0);
    -webkit-transition: all .3s;
    transition: all .3s;
    margin-right: 0;
    position: relative;
}

.searchbar .search-input input {
    margin: 0;
    height: 1.4rem;
}

.searchbar.searchbar-active .searchbar-cancel {
    right: 0.5rem;
    opacity: 1;
}

.searchbar.searchbar-active .searchbar-cancel + .search-input {
    margin-right: 2.2rem;
}

.search-input {
    position: relative;
}

.search-input input {
    box-sizing: border-box;
    width: 100%;
    height: 1.4rem;
    display: block;
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.25rem;
    font-family: inherit;
    color: #3d4145;
    font-size: 0.7rem;
    font-weight: normal;
    padding: 0 0.5rem;
    background-color: #fff;
    border: 1px solid #b4b4b4;
}

.search-input input::-webkit-input-placeholder {
    color: #cccccc;
    opacity: 1;
}

.search-input i {
    position: absolute;
    font-size: 0.9rem;
    color: #b4b4b4;
    top: 0;
    left: 0.3rem;
    line-height: 1.4rem;
}

.search-input i + input {
    padding-left: 1.4rem;
}

.fui-card {
    background: #fff;
    box-shadow: 0 0.1rem 0.1rem rgba(204, 204, 204, 0.3);
    border: 1px solid #ccc;
    margin: 0.5rem;
    position: relative;
    border-radius: 0.1rem;
    font-size: 0.7rem;
    padding-top: .5rem;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: background-color;
    transition-property: background-color;
}

.fui-card.fui-card-full {
    margin: 0;
    border: none;
    border-radius: none;
    box-shadow: none;
}

.fui-card:not(.fui-card-list):active {
    background: #e5e5e5;
}

.fui-card .fui-card-title {
    padding: 0rem .5rem 0 .5rem;
}

.fui-card .fui-card-title span {
    width: 100%;
    display: block;
}

.fui-card .fui-card-title .title {
    line-height: 1rem;
    font-size: .85rem;
}

.fui-card .fui-card-title .subtitle {
    color: #666;
    padding: .3rem 0;
}

.fui-card .fui-card-image {
    padding: .3rem .3rem 0 .3rem;
}

.fui-card .fui-card-image img {
    width: 100%;
}

.fui-card .fui-card-content {
    padding: 0 .5rem .5rem .5rem;
    line-height: 1rem;
    font-size: 0.7rem;
    color: #445;
}

.fui-card .fui-card-footer {
    border-top: 1px solid #f5f5f5;
    padding: .6rem .5rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.fui-card .fui-card-footer .text {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.fui-card .fui-card-footer .remark:after {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 0.3rem;
    width: 0.3rem;
    border-width: 2px 2px 0 0;
    border-color: #C8C8CD;
    border-style: solid;
    position: relative;
    top: -1px;
    margin-left: .3em;
}

.fui-card-header {
    text-align: center;
    margin: 0.8rem .5rem .8rem .5rem;
    font-size: 0.7rem;
    color: #666;
}

.fui-card-header span {
    display: inline-block;
    background: #cecece;
    padding: .2rem .5rem;
    border-radius: 0.2rem;
    color: #fff;
}

.fui-card.fui-card-list {
    padding: .05rem;
    padding-bottom: 0;
}

.fui-card .fui-card-first {
    padding: .5rem .2rem;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: background-color;
    transition-property: background-color;
}

.fui-card .fui-card-first .image {
    position: relative;
}

.fui-card .fui-card-first img {
    width: 100%;
    display: block;
}

.fui-card .fui-card-first .text {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    bottom: 0;
    width: 100%;
    padding: .4rem .3rem;
    color: #fff;
    font-size: 0.8rem;
}

.fui-card .fui-card-item {
    border-top: 1px solid #f5f5f5;
    padding: .2rem .5rem .2rem .5rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: background-color;
    transition-property: background-color;
}

.fui-card .fui-card-item img {
    width: 2rem;
    height: 2rem;
    display: table-cell
}

.fui-card .fui-card-item .text {
    padding: 0 .5rem;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.fui-card .fui-card-item .text.left {
    padding: 0;
}

.fui-card-first:active,
.fui-card-item:active {
    background: #e5e5e5;
}

.fui-card .fui-card-info {
    padding: 0 .5rem;
    padding-top: .5rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.fui-card .fui-card-info span {
    display: block;
    padding: 0 .5rem;
    font-size: 0.7rem;
}

.fui-card .fui-card-info .subtitle {
    padding-top: 0.2rem;
    color: #666;
    font-size: 0.65rem;
}

.fui-card .fui-card-info img {
    width: 2rem;
    height: 2rem;
}

.fui-card .fui-card-info .text {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.fui-card .fui-card-btns {
    padding: .6rem .5rem;
    border-top: 1px solid #f5f5f5;
}

.fui-card .fui-card-btns a {
    display: table-cell;
    width: 1%;
    text-align: center;
    text-decoration: none;
    color: #666;
}

.fui-card .fui-card-btns a:first-child {
    text-align: left;
}

.fui-card .fui-card-btns a:last-child {
    text-align: right;
}

.fui-card .fui-card-btns a:active {
    color: #999;
}


.fui-list-group {
    background-color: #fff;
    position: relative;
    margin-top: .5rem;
    position: relative;
}

.fui-list-group.fui-list-group-o {
    margin: 0 .5rem;
    border-radius: .5rem;
    margin-top: 0.5rem;
}

.fui-list-group-title {
    padding: 0 .6rem;
    font-size: .7rem;
    color: #333;
    height: 1.8rem;
    line-height: 1.8rem;

}


.fui-list {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding: .4rem .6rem;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    overflow: hidden;

}

.fui-list.align-start {
    -webkit-box-align: start;
    -webkit-align-items: start;
    -ms-flex-align: start;
    align-items: start
}

.fui-list:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: .5rem;
    height: 1px;

    border-top: 1px solid #ebebeb;
    color: #ebebeb;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: .5rem;
}

.fui-list:first-child:before {
    display: none;
}


.fui-list:active {
    background: #ececec;
}

.fui-list.noclick:active {
    background: #fff;
}

.fui-list a {
    color: #666;
}

.fui-list-media {

    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-shrink: 0;
    -ms-flex: 0 0 auto;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    -webkit-box-lines: single;
    -moz-box-lines: single;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
    box-sizing: border-box;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    margin-right: .6rem;
    color: #aaa;
    position: relative;
}

.fui-list-media .title {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 0.7rem;
    height: 1rem;
    right: 0;
    left: 0;
    line-height: 1rem;
    font-size: 0.6rem;
    padding: 0 0.15rem;
    position: absolute;
    bottom: 0;
    text-align: center;
    vertical-align: top;
    z-index: 100;
}

.fui-list-media img {

    width: 2.5rem;
    height: 2.5rem;


}


.fui-list-media .badge {
    background: red none repeat scroll 0 0;
    border-radius: 0.5rem;
    color: white;
    font-size: 0.7rem;
    height: 0.9rem;
    right: -.35rem;
    line-height: 0.9rem;

    min-width: 0.9rem;
    padding: 0 0.15rem;
    position: absolute;
    top: -.3rem;
    text-align: center;
    vertical-align: top;
    z-index: 100;
}

.fui-list-inner {
    position: relative;
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: block;
}

.fui-list-inner .subtitle {
    position: relative;
    font-size: 0.65rem;
    color: #666;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.fui-list-inner .text {
    position: relative;
    font-size: 0.65rem;
    color: #666;


}

.fui-list-inner .bar {
    position: relative;
    font-size: 0.65rem;
    color: #666;
    margin-top: .3rem;
    padding-top: 0.2rem;
    height: 1.4rem;
    line-height: 1.4rem;
}

.fui-list-inner .bar:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;

    border-top: 1px solid #ebebeb;
    color: #ebebeb;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);


}

.fui-list-inner .title {
    position: relative;
    font-size: 0.7rem;
    color: #000;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    height: 1rem;
    line-height: 1rem;
}

.fui-list-inner .row {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin: 0;
    height: 1rem;
    line-height: 1rem;

}

.fui-list-inner .row .row-text {
    font-size: 0.85rem;
    color: #333;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.fui-list-inner .row .row-remark {
    font-size: 0.75rem;
    color: #666;
    text-align: right;
}

.fui-list .angle:after {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);

    height: 0.4rem;
    width: 0.4rem;
    border-width: 1px 1px 0 0;
    border-color: #b2b2b2;
    border-style: solid;
    position: relative;

    top: 0px;
    margin-left: .1em;
}

.fui-list-angle {
    position: relative;
    vertical-align: middle;

    margin-right: .6rem;
    margin-left: .5rem;
}

.fui-list-angle .angle:after {
    position: absolute;
    top: 50%;
    margin-top: -.3rem;
}

.fui-list-inner .subtitle.overflow,
.fui-list-inner .title.overflow {
    word-break: break-all;
    overflow: auto;
    white-space: normal;
    height: auto;

}

.checkbox-inline, .radio-inline {
    position: relative;
    font-size: .7rem;
    color: #666;
    padding-left: 1.2rem;
    vertical-align: middle;


}

.checkbox-inline input[type=checkbox],
.radio-inline input[type=radio] {
    position: absolute;
    margin-left: -1.2rem;

}

.fui-radio, .fui-checkbox {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    position: relative;
    width: .95rem;
    height: .95rem;
    border: 1px solid #DFDFDF;
    outline: 0;
    border-radius: 16px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    border: 1px solid #DFDFDF;
    background: #fff;
    vertical-align: middle;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
}

.fui-checkbox {
    border-radius: 0;
    width: .9rem;
    height: .9rem;
}

.fui-checkbox:checked:before {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
    height: 0.3rem;
    width: 0.5rem;
    border-width: 1px 1px 0 0;
    border-color: #d9d9d9;
    border-style: solid;
    position: relative;
    top: -.1rem;
    margin-left: .15rem;
}

.fui-radio:checked:before {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
    height: 0.25rem;
    width: 0.45rem;
    border-width: 1px 1px 0 0;
    border-color: #d9d9d9;
    border-style: solid;
    position: relative;

    top: -.05rem;
    margin-left: .2rem;

}

.fui-checkbox.fui-checkbox:checked {
    border: 1px solid #d9d9d9;
}

.fui-checkbox.fui-checkbox-default:checked,
.fui-radio.fui-radio-default:checked {
    border-color: #d9d9d9;
    background-color: #fff;

}

.fui-checkbox.fui-checkbox-success:checked,
.fui-radio.fui-radio-success:checked {
    background-color: #04BE02;
    border: 1px solid #04BE02;
}

.fui-checkbox.fui-checkbox-primary:checked,
.fui-radio.fui-radio-primary:checked {
    background-color: #0290be;
    background-color: #1492fb;
    border: 1px solid #ffffff;
}

.fui-checkbox.fui-checkbox-warning:checked,
.fui-radio.fui-radio-warning:checked {
    background-color: #ff8000;
}

.fui-checkbox.fui-checkbox-danger:checked,
.fui-radio.fui-radio-danger:checked {

    background-color: #ff5555;
    border: 1px solid #ff5555;
}

.fui-checkbox.fui-checkbox-success:checked:before,
.fui-checkbox.fui-checkbox-primary:checked:before,
.fui-checkbox.fui-checkbox-warning:checked:before,
.fui-checkbox.fui-checkbox-danger:checked:before,
.fui-radio.fui-radio-success:checked:before,
.fui-radio.fui-radio-primary:checked:before,
.fui-radio.fui-radio-warning:checked:before,
.fui-radio.fui-radio-danger:checked:before {
    border-color: #fff;
}

.fui-message {
    position: relative;
    padding-top: 2rem;
    background: rgba(0, 0, 0, 0);
    width: 100%;
}

.fui-message.fui-message-popup {
    height: 100%;
    width: 100%;
    background: #fff;
}

.fui-message .icon {
    margin: auto;
    text-align: center;

}

.fui-message .icon i {
    font-size: 4rem;
    color: #999;
}

.fui-message .title {
    font-size: 1.2rem;
    text-align: center;
    padding: .5rem;
    font-weight: bold;
    color: #555
}

.fui-message .content {
    font-size: .85rem;
    text-align: center;
    margin: .5rem 1rem;
    color: #666;
    margin-bottom: 1rem;
}

.fui-message .btn {
    margin: .5rem 1rem;
}

.fui-message .button {
    text-align: center;
}


.fui-icon-group {
    position: relative;
    overflow: hidden;

    background: #fff;
}

.fui-icon-group:brfore {

}

.fui-icon-group .fui-icon-col {
    width: 25%;
    height: auto;
    position: relative;
    padding: 0.5rem 0;
    text-align: center;
    transition: background-color 300ms;
    -webkit-transition: background-color 300ms;
    float: left;
    border: none !important;
}

.fui-icon-group .fui-icon-col:active {
    background: #ececec;
}

.fui-icon-group .fui-icon-col:last-child:before {
    display: none
}

.fui-icon-group .fui-icon-col .icon {
    height: 2.2rem;
    margin: auto;
    text-align: center;
    line-height: 2.2rem;
}

.member-page .fui-icon-group .fui-icon-col .icon {
    height: 1.8rem;
    line-height: 2.1rem;
}

.fui-icon-group.col-3 .fui-icon-col {
    width: 33.3%;
}

.fui-icon-group.col-5 .fui-icon-col {
    width: 20%;
}

.fui-icon-group .fui-icon-col .icon img {
    height: 2.2rem;
    width: 2.2rem;
}

.fui-icon-group.radius .fui-icon-col img {
    border-radius: .5rem;
}

.fui-icon-group.circle .fui-icon-col img {
    border-radius: 2.2rem;
}

.fui-icon-group.col-3 .fui-icon-col.radius img {
    border-radius: 33.3%;
}

.fui-icon-group.col-5 .fui-icon-col.radius img {
    border-radius: 20%;
}

.fui-icon-group .fui-icon-col .text {
    font-size: 0.6rem;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0.2rem;

    color: #000;
}

.fui-icon-group.noborder {
    border-top: 0;
}

.fui-icon-group.noborder .fui-icon-col:before {
    border: 0;
}


.fui-icon-group .fui-icon-col .icon i {
    color: #aaa;
    font-size: 1.2rem;
    margin-top: .35rem;
}

.fui-icon-group .fui-icon-col .badge {
    background: red none repeat scroll 0 0;
    border-radius: 0.5rem;
    color: white;
    font-size: 0.6rem;
    height: 0.8rem;
    left: 50%;
    line-height: 0.8rem;
    margin-left: 0.35rem;
    min-width: 0.8rem;
    padding: 0 0.2rem;
    position: absolute;
    top: 0.5rem;
    vertical-align: top;
    text-align: center;
    z-index: 100;

}

.fui-block-group {
    background: #fff;
    border-bottom: 1px solid #ececec;
    position: relative;
    margin-top: .5rem
}

.fui-block-group .fui-block-child {
    height: auto;
    float: left;
    padding: 0.4rem 0;
    background: #fff;
    transition: background-color 300ms;
    -webkit-transition: background-color 300ms;
    position: relative;
}

.fui-block-group .fui-block-child:before {
    content: "";
    width: 0px;
    border-right: 1px solid #ececec;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
}

.fui-block-group .fui-block-child:after {
    content: "";
    height: 0px;
    border-bottom: 1px solid #ececec;
    position: absolute;
    right: 0;
    left: 0;
    bottom: 0;
}

.fui-block-group.col-2 .fui-block-child {
    width: 50%
}

.fui-block-group.col-3 .fui-block-child {
    width: 33.3%
}

.fui-block-group.col-4 .fui-block-child {
    width: 25%
}

.fui-block-group.col-5 .fui-block-child {
    width: 20%
}

.fui-block-group .fui-block-child:active {
    background: #ececec;
}

.fui-block-group .fui-block-child .icon {
    height: 2.7rem;
    padding-top: 1rem;
    text-align: center;
    font-size: 1.4rem;
    line-height: 1.5rem;
}

.fui-block-group .fui-block-child .icon i {
    font-size: 1.8rem;
}

.fui-block-group .fui-block-child .title {
    height: 1rem;
    font-size: 0.6rem;
    line-height: 1.5rem;
    text-align: center;
    color: #666;
}

.fui-block-group .fui-block-child .title.bigsize {
    font-size: 0.7rem;
}

.fui-block-group .fui-block-child .text {
    height: 1.2rem;
    font-size: 0.5rem;
    text-align: center;
    color: #666;
    line-height: 1rem;
}

.fui-block-group .fui-block-child .text span {
    color: #feb312;
}

.fui-block-group .fui-block-child .text.remark {
    color: #a9a9a9;
    font-size: 0.6rem;
    line-height: 1.2rem;
}

.fui-block-group .fui-block-child .num {
    height: 0.9rem;
    text-align: center;
    font-size: 0.8rem;
    color: #fb6665;
    line-height: 1.4rem;
}

.fui-block-group .fui-block-dots {
    height: 0.9rem;
    text-align: center;
    line-height: 0.6rem;
}

.fui-block-group .fui-block-dots a {
    height: 0.3rem;
    width: 0.3rem;
    background: #e6e6e6;
    border-radius: 0.4rem;
    display: inline-block;
}

.fui-block-group .fui-block-dots a.on {
    background: #fa5453;
}


.fui-number {
    backface-visibility: hidden;
    box-sizing: border-box;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    font-size: 0.8rem;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0;
    height: 1.5rem;
    width: 5rem;

}

.fui-number:before {
    content: " ";
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #d9d9d9;
}

.fui-number:after {
    content: " ";
    position: absolute;
    z-index: 2;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #d9d9d9;
}

.fui-number .num {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden;
    line-height: inherit;
    color: #666;
    text-align: center;
    border: 0;
    font-size: .7rem;
}

.fui-number .minus, .fui-number .plus {
    height: inherit;
    width: 1.5rem;
    font-size: 1rem;
    font-weight: bold;
    color: #999;
    position: relative;
    text-align: center;
    line-height: 1.5rem;
    background: #fff;
    z-index: 1;
}

.fui-number .minus:before, .fui-number .plus:before {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    border-left: 1px solid #d9d9d9;
}

.fui-number .minus:after, .fui-number .plus:after {
    content: " ";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    border-right: 1px solid #d9d9d9;
}

.fui-number .minus.disabled, .fui-number .plus.disabled {
    background: #fff;
    color: #ebebeb;
}

.fui-number.small {

    height: 1.1rem;
    width: 3.6rem;
    line-height: 1.2rem;
}

.fui-number.small .minus, .fui-number.small .plus {

    width: 1.1rem;
    line-height: 1.1rem;


    font-size: .65rem;
    color: #666;
    background: #fff;
}


.fui-line {
    height: auto;
    display: block;
    position: relative;
    background: inherit;
}

.fui-line:before {
    content: " ";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: #d5d5d5;
}

.fui-line .text {
    height: 100%;
    width: auto;
    margin: auto;
    background: inherit;
    font-size: 0.7rem;
    display: table;
    position: relative;
    padding: 0.4rem 0.3rem;
}

.fui-line .text.big {
    font-size: 0.8rem
}

.fui-line .text.large {
    font-size: 1rem
}


.fui-searchbar .searchbar.center {
    background: none;
}

.fui-searchbar .searchbar.center .search {
    text-align: center;
}

.fui-searchbar.bar:after {
    height: 0;
}

.fui-searchbar .searchbar.center .search::-webkit-input-placeholder {
    height: auto;
    width: 100px;
    color: #979797;
    text-align: center;
    position: relative;
}

.fui-searchbar .searchbar.center .search::-webkit-input-placeholder:before {
    content: " ";
    height: 1rem;
    width: 1rem;
    top: 0;
    left: 50%;
    margin-left: -3rem;
    position: absolute;
    background-size: 0.8rem;
}


.fui-article {
    position: relative;
    height: auto;
    padding: 1rem .5rem;
    background: #fff;

}

.fui-article .title {
    width: 100%;
    font-size: 0.9rem;
    word-break: break-all;
    color: #333;
}

.fui-article .subtitle {
    padding-top: 0.2rem;
    width: 100%;
    font-size: 0.7rem;
    word-break: break-all;
    color: #aaa;

}

.fui-article .content {
    padding: 0;
    font-size: 0.75rem;
    line-height: 1rem;
    height: auto;
}


.fui-loading-text {
    position: relative;
    text-align: center;
    background: inherit;
}

.fui-loading-text .fui-preloader {
    margin-top: .5rem;
}

.fui-loading-text .text {
    position: relative;
    color: inherit;
    font-size: 0.75rem;
    padding-bottom: .5rem;
}


.fui-modal {
    position: absolute;
    width: 100%;
    z-index: 1001;


}

.fui-modal.picker-modal {
    left: 0;
    bottom: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    -webkit-transition-property: -webkit-transform;
    transition-property: transform;

}

.fui-modal.picker-modal.in {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}


.fui-modal.picker-modal.out {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
}

.fui-modal.popup-modal {
    left: 0;
    bottom: 0;
    top: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    -webkit-transition-property: -webkit-transform;
    transition-property: transform;

}

.fui-modal.popup-modal.in {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.fui-modal.popup-modal.out {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
}

.fui-modal.notify-modal {
    position: absolute;
    top: 0;
    text-align: center;
    width: 100%;
    height: auto;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    z-index: 1001;
}

.fui-modal.notify-modal.in {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.fui-modal.notify-modal.out {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
}


.fui-actionsheet {
    position: relative;
    width: 100%;
}

.fui-actionsheet-title {
    background: #ececec;
    height: 2.2rem;
    line-height: 2.2rem;
    font-size: 0.75rem;
    text-align: center;
    position: relative;
}

.fui-actionsheet a {
    width: 100%;
    display: inline-block;
    text-align: center;
    background: #fff;
    padding: 0.5rem 0;
    border-top: 1px solid #d9d9d9;
    color: #666;
    font-size: 0.8rem;
}

.fui-actionsheet a:first-child {
    border: none;
}

.fui-actionsheet a.cancel {

    border: none;
    background: #ef4f4f;
    color: #fff;
}

.fui-actionsheet a:not(.cancel):active {
    background: #e5e5e5;
}

.fui-actionsheet.fui-actionsheet-o {
    padding: .3rem;
}

.fui-actionsheet.fui-actionsheet-o a {
    border-radius: 0.2rem;
    margin-bottom: 0.2rem;
    border: none;
}


.fui-according-group {
    margin-top: 0.5rem;
    background-color: #fff;
    line-height: 1.4;
    font-size: 0.8rem;
    overflow: hidden;
    position: relative;
}

.fui-according-group.fui-according-group-o {
    margin: 0 .5rem;
    border-radius: .5rem;
    margin-top: 0.5rem;
}


.fui-according {
    position: relative;
}

.fui-according:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: .5rem;
}

.fui-according:first-child:before {
    display: none;
}

.fui-according-header {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;


    padding: 0.6rem 0.6rem 0.5rem;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: background-color;
    transition-property: background-color;


}

.fui-according-header:active {
    background: #d9d9d9;
}

.fui-according-header .text {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;

    color: #000;
    font-size: 0.7rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

}

.fui-according-header .remark {
    color: #888;
    text-align: right;

    font-size: 0.65rem;
    margin-right: 0.2rem;
}

.fui-according-header .remark:after {
    content: "  ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 0.4rem;
    width: 0.4rem;
    border-width: 1px 1px 0 0;
    border-color: #C8C8CD;
    border-style: solid;
    position: relative;
    top: 0px;
    margin-left: .3em;
}

.fui-according.expanded .remark:after,
.fui-according-header:active .remark:after {
    content: "  ";
    display: inline-block;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
    height: 0.4rem;
    width: 0.4rem;
    border-width: 1px 1px 0 0;
    border-color: #C8C8CD;
    border-style: solid;
    position: relative;
    top: -1px;
    margin-left: .3em;
}

.fui-according-content .content-block {
    padding: .5rem .5rem;
    color: #666;
    box-sizing: border-box;
}

.content-images {
    overflow-x: hidden;
}

.content-images table,
.content-images div {
    width: 100%;
    display: block;
    max-width: 100%;
}

.content-images img {
    display: inline-block;
    vertical-align: middle;
    max-width: 100%;
}

.fui-according-content {
    position: relative;
    width: 100%;
    height: auto;
    display: none;
    overflow: hidden;
    -moz-transition-duration: 200ms;
    -webkit-transition-duration: 200ms;
    transition-duration: 200ms;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.fui-according.expanded .fui-according-content {
    display: block;
    height: auto;
    margin-top: 0rem;
}

.fui-picker {
    position: relative;
    width: 100%;
    background: #fff;
    height: 13rem;


}


.fui-picker-header {

    height: 2rem;
    width: 100%;
    box-sizing: border-box;
    font-size: .8rem;
    position: relative;
    margin: 0;
    z-index: 500;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    background: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;

    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;


}

.fui-picker-header:before {
    content: "";
    position: absolute;
    left: 0;
    top: -1px;;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #D9D9D9;
    -webkit-transform-origin: 0 100%;
    -ms-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);

}

.fui-picker-header .center {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    margin: 0 .5rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 2rem;
    color: #333;
    text-align: center;
}

.fui-picker-header .left {
    padding-left: .3rem;
    line-height: 2rem;

}

.fui-picker-header .right {
    padding-right: .3rem;
    line-height: 2rem;
}

.fui-picker-header a {

    font-size: 0.75rem;
    padding: 0;
    margin: 0;
    top: 0;
    display: block;
    padding: 0 .2rem;


}


.fui-picker-content {
    height: 100%;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 0;
    text-align: right;
    font-size: 1rem;
    -webkit-mask-box-image: -webkit-linear-gradient(bottom, transparent, transparent 5%, white 20%, white 80%, transparent 95%, transparent);
    -webkit-mask-box-image: linear-gradient(to top, transparent, transparent 5%, white 20%, white 80%, transparent 95%, transparent);

}

.fui-picker-col {
    overflow: hidden;
    position: relative;
    max-height: 100%;


}

.fui-picker-col.fui-picker-col-left {
    text-align: left;
}

.fui-picker-col.fui-picker-col-center {
    text-align: center;
}

.fui-picker-col.fui-picker-col-right {
    text-align: right;

}

.fui-picker-col.fui-picker-col-divider {
    color: #3d4145;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
}

.fui-picker-col-normal {
    width: 100%;
}

.fui-picker-wrapper {
    -webkit-transition: 300ms;
    transition: 300ms;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;

}

.fui-picker-item {

    height: auto;
    line-height: 1.5rem;
    padding: 0 10px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #aaa;
    left: 0;
    top: 0;


    box-sizing: border-box;
    -webkit-transition: 300ms;
    transition: 300ms;
}

.fui-picker-item.selected {
    color: #333;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-transform: rotateX(0deg);
    transform: rotateX(0deg);

}

.fui-picker-highlight {
    height: 1.5rem;
    box-sizing: border-box;
    position: absolute;
    left: 0;
    width: 100%;
    top: 50%;
    margin-top: -.75rem;
    pointer-events: none;
    background: rgba(255, 255, 255, 0.05);
}

.fui-picker-highlight:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: auto;
    right: auto;
    height: 1px;
    width: 100%;
    background-color: #d9d9d9;
    display: block;
    z-index: 15;
    -webkit-transform-origin: 50% 0%;
    transform-origin: 50% 0%;
}

.fui-picker-highlight:after {
    content: ' ';
    position: absolute;
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    height: 1px;
    width: 100%;
    background-color: #d9d9d9;
    display: block;
    z-index: 15;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2) {
    .fui-picker-highlight:before {
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
    }
}

@media only screen and (-webkit-min-device-pixel-ratio: 3) {
    .fui-picker-highlight:before {
        -webkit-transform: scaleY(0.33);
        transform: scaleY(0.33);
    }
}

.fui-picker .col-province {
    width: 5rem;
}

.fui-picker .col-city {
    width: 6rem;
}

.fui-picker .col-area {
    width: 5rem;
}

.fui-swipe {
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.fui-swipe-wrapper {
    box-sizing: content-box;
    display: flex;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transition-property: transform;
    width: 100%;
    z-index: 1;
}

.fui-swipe-item {

    -webkit-flex-shrink: 0;
    -ms-flex: 0 0 auto;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    position: relative;
    float: left;

}

.fui-swipe-item,
.fui-swipe-wrapper {
    -webkit-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;

}

.fui-swipe-wrapper .fui-swipe-item img {
    width: 100%;
    display: block;
}

.fui-swipe-page {
    position: absolute;
    text-align: center;
    -webkit-transition: 300ms;
    transition: 300ms;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    z-index: 10;
    bottom: 10px;
    width: 100%;

}

.fui-swipe-bullet {
    background: #fff none repeat scroll 0 0;
    border-radius: 100%;
    display: inline-block;
    height: 8px;
    opacity: 0.5;
    width: 8px;
    margin: 0 1px;
}

.fui-swipe-bullet.active {
    background: #ff5555 none repeat scroll 0 0;
    opacity: 1;
}


.fui-swipe-button {
    position: absolute;
    top: 50%;
    z-index: 10;

    background: rgba(0, 0, 0, .2);
    text-align: center;
    width: 2rem;
    height: 2rem;
    border-radius: .2rem;
    -webkit-transition: 300ms;
    transition: 300ms;
    margin-top: -1rem;
}

.fui-swipe-button:active {
    background: rgba(0, 0, 0, .5);
}

.fui-swipe-button.left {

    left: -10px;
}

.fui-swipe-button.right {
    right: -10px;
}

.fui-swipe-button.left:after,
.fui-swipe-button.right:after {

    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: .6rem;
    width: .6rem;

    border-color: #fff;
    border-style: solid;
    position: relative;
    top: .4rem;
    opacity: 0.5;

}

.fui-swipe-button.left:after {
    margin-left: .8rem;
    border-width: 0 0 2px 2px;
}

.fui-swipe-button.right:after {

    margin-right: .6rem;
    border-width: 2px 2px 0 0;

}

.fui-swipe-button:active:after {
    opacity: 0.8;
}


.fui-stars i {
    color: #999;
    font-size: .8rem;
    margin-right: .1rem;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: color;
    transition-property: color;
    font-size: 1rem;
}

.fui-stars i.selected {
    color: #f90
}

.fui-stars.success i.selected {
    color: #04ab02
}

.fui-stars.primary i.selected {
    color: #0290be
}

.fui-stars.warning i.selected {
    color: #ff8000
}

.fui-stars.danger i.selected {
    color: #ef4f4f
}


.fui-labeltext {
    position: relative;
    border: 1px solid #d9d9d9;
    display: table-cell;
    border-radius: .2rem;
    height: 1.5rem;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden;
}

.fui-labeltext.fui-labeltext-success {
    border: 1px solid #04ab02
}

.fui-labeltext.fui-labeltext-primary {
    border: 1px solid #0290be
}

.fui-labeltext.fui-labeltext-warning {
    border: 1px solid #ff8000
}

.fui-labeltext.fui-labeltext-danger {
    border: 1px solid #ef4f4f
}

.fui-labeltext .label {

    background: #d9d9d9;
    font-size: 0.7rem;
    float: left;
    height: 1.5rem;
    line-height: 1.5rem;

    padding: 0 0.3rem;
}

.fui-labeltext.fui-labeltext-success .label {
    background: #04ab02;
    color: #fff;
}

.fui-labeltext.fui-labeltext-primary .label {
    background: #0290be;
    color: #fff;
}

.fui-labeltext.fui-labeltext-warning .label {
    background: #ff8000;
    color: #fff;
}

.fui-labeltext.fui-labeltext-danger .label {
    background: #ef4f4f;
    color: #fff;
}

.fui-labeltext .text {
    float: left;
    height: 1.5rem;
    line-height: 1.5rem;
    padding: 0 .2rem 0 0.7rem;
}

.pulldown-loading, .pullup-loading {
    position: relative;
    width: 100%;
    text-align: center;
    color: #999;
    height: 2.2rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}

.pulldown-loading .fui-preloader,
.pullup-loading .fui-preloader {
    visibility: hidden;
}

.refreshing .pulldown-loading .fui-preloader {
    visibility: visible;
}

.refreshing .pulldown-loading .arrow {
    display: none;
}

.loading .pullup-loading .fui-preloader {
    visibility: visible;
}

.loading .pullup-loading .arrow {
    display: none;
}

.pulldown-loading .arrow,
.pullup-loading .arrow {
    width: 0.65rem;
    height: 1rem;
    position: absolute;
    top: 50%;
    margin-left: -.85rem;
    margin-top: -0.5rem;
    background: no-repeat center;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2026%2040'%3E%3Cpolygon%20points%3D'9%2C22%209%2C0%2017%2C0%2017%2C22%2026%2C22%2013.5%2C40%200%2C22'%20fill%3D'%238c8c8c'%2F%3E%3C%2Fsvg%3E");
    background-size: 0.65rem 1rem;
    z-index: 10;
    -webkit-transform: rotate(0deg) translate3d(0, 0, 0) scale(0.7);
    transform: rotate(0deg) translate3d(0, 0, 0) scale(0.7);
    display: inline-block;
}

.pulldown-loading .text,
.pullup-loading .text {
    display: inline-block;
    font-size: 0.75rem;
    padding-left: .1rem;
    height: 2.2rem;
    line-height: 2.2rem;
}

.pullup-loading .arrow {

    -webkit-transform: rotate(180deg) scale(0.7);
    transform: rotate(180deg) scale(0.7);
}

.pulldown-loading .arrow.reverse,
.pullup-loading .arrow.reverse {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
}

.pulldown-loading .arrow.reverse {
    -webkit-transform: rotate(-180deg) scale(0.7);
    transform: rotate(-180deg) scale(0.7);
}

.pullup-loading .arrow.reverse {
    -webkit-transform: rotate(0deg) scale(0.7);
    transform: rotate(0deg) scale(0.7);
}


.infinite-loading {
    position: relative;
    text-align: center;
    height: 2.2rem;
    line-height: 2.2rem;
}

.infinite-loading .text {
    position: relative;
    display: inline-block;
    padding-left: 1.1rem;
}

.infinite-loading .fui-preloader {
    position: absolute;
    top: 50%;
    margin-top: -.45rem;
}


.fui-menu-group {
    height: auto;
    background: #fff;
    margin-top: 0.5rem;
}

.fui-menu-group .fui-menu-item {
    padding: 0.5rem;
    text-align: center;
    position: relative;
    color: #666;
    font-size: 0.75rem;
    width: 1%;
    display: table-cell;
    -webkit-user-select: none;
    -moz-user-select: none;
    transition: background-color 300ms;
    -webkit-transition: background-color 300ms;
}

.fui-menu-group .fui-menu-item:active {
    background: #ececec;
}

.fui-menu-group .fui-menu-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.5rem;
    bottom: 0.5rem;
    border-left: 1px solid #eee;
}

.fui-menu-group .fui-menu-item:first-child:before {
    border: 0;
}

.fui-menu-group .fui-menu-item i {
    vertical-align: middle;
}

.fui-tag {
    display: inline-block;
}

.fui-tag-danger {
    background: #ff8000;
    color: #fff;
    font-size: 0.6rem;
    line-height: 0.6rem;
    -webkit-border-radius: 0.2rem;
    border-radius: 0.2rem;
    font-style: normal;
    padding: 0.2rem;
}

.bigprice {
    font-size: .75rem;
}

.c666 {
    color: #666;
}

.c999 {
    color: #999;
}

.c000 {
    color: #000;
}

.c333 {
    color: #333;
}

.fui-cell-group .fui-cell .fui-cell-label.c000 {
    color: #000;
}

.fui-cell-group .fui-cell .fui-cell-info.c000 {
    color: #000;
}

.fui-cell-group .fui-cell .fui-cell-text.c666 {
    color: #666
}

.fui-list-angle.c000 {
    color: #000;
}

.fui-list-media.c000 {
    color: #000;
}

.fui-list-angle.f26 {
    font-size: .65rem
}

.fui-list .fui-list-angle.c000 .angle:after {
    border-color: #000;
}

.fui-list-inner .subtitle.c999 {
    color: #999;
}

.fui-list-inner .subtitle.f24 {
    font-size: .65rem
}


.fui-cell-group .fui-cell.fui-cell-textarea {
    height: 3rem;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: flex-start;
    align-items: flex-start;

}

.flex-start {
    align-self: flex-start;
    -webkit-align-self: flex-start;
}

.fui-btn-confirm {
    height: 2.25rem;
    display: block;
    text-align: center;
    color: #fff;
    line-height: 2.25rem;
    background: #ff5555;
    font-size: .75rem;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0
}

.disable {
    background: #ccc;
    border-color: #ccc;
    color: #fff
}

.fui-cell-group .fui-cell .fui-cell-remark.noremark {
    font-size: .65rem;
    color: #000;
}

.fui-cell-group .fui-cell.no-border:before {
    display: none;
}

.fui-list-group .fui-list.no-border:before {
    display: none;
}


.cycle-tip {
    display: inline-table;
    width: 2.3rem;
    height: 0.8rem;
    border-radius: 4px;
    font-size: 0.5rem;
    color: #fff;
    line-height: 0.9rem;
    text-align: center;
    position: relative;
    padding-top: 0.05rem;
    margin-right: 2px;
    background: rgb(255, 22, 45);
    background: -moz-radial-gradient(bottom left, ellipse cover, rgba(255, 115, 94, 1) 30%, rgba(255, 51, 51, 1) 57%);
    background: -webkit-gradient(radial, bottom left, 0px, top left, 100%, color-stop(30%, rgba(255, 115, 94, 1)), color-stop(57%, rgba(255, 51, 51, 1)));
    background: -webkit-radial-gradient(bottom left, ellipse cover, rgba(255, 115, 94, 1) 30%, rgba(255, 51, 51, 1) 57%);
    background: -o-radial-gradient(bottom left, ellipse cover, rgba(255, 115, 94, 1) 30%, rgba(255, 51, 51, 1) 57%);
    background: -ms-radial-gradient(bottom left, ellipse cover, rgba(255, 115, 94, 1) 30%, rgba(255, 51, 51, 1) 57%);
    background: radial-gradient(ellipse at bottom left, rgba(255, 115, 94, 1) 30%, rgba(255, 51, 51, 1) 57%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff4d81', endColorstr='#ff162d', GradientType=1);
}

.cycle-tip.cycle-tip2 {

}


.date-picker.order-date-picker {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    display: none;
}

.date-picker.order-date-picker .order-chose-close .icon {
    font-size: 1rem;
}

.date-picker.order-date-picker .order-chose-close {
    height: 1.9rem;
    line-height: 1.9rem;
    padding-right: 0.8rem;
}

.date-picker.order-date-picker .order-alert {
    border-radius: 4px;
    width: 17.25rem;
    height: 18.9rem;
    z-index: 999;
    margin: 5rem auto 0;
    background: #fff;
    display: none;
    align-items: center;
}

.date-picker.order-date-picker .order-alert-info {
    width: 100%;
    height: 100%;
    margin: auto;
    background: #fff;
    border-radius: 0.2rem;
    overflow: hidden;
}

.date-picker.order-date-picker .date-alert.show {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
}

.date-chose {
    height: auto;
    width: 100%;

    display: -webkit-flex;
    display: flex;
    flex-direction: column;
    -webkit-flex-direction: column;
}

.date-picker.order-date-picker .date-chose .year-month {
    margin: auto;
    height: 2rem;
    width: 100%;
    background: #f7f7f7;
    font-size: 0.65rem;
    line-height: 2rem;
    text-align: center;
    padding: 0 2rem;
    color: #666;
}

.date-picker.order-date-picker .date-chose .detail-day {
    -webkit-flex: 1;
    flex: 1;
    display: -webkit-flex;
    display: flex;
    flex-direction: column;
    -webkit-flex-direction: column;
    background: #fff;
    text-align: center;
    font-size: 0.65rem;
    padding: 0.5rem 0.2rem 0;
}

.date-picker.order-date-picker .date-chose .detail-day .week {
    height: 2.1rem;
    line-height: 2.1rem;
    width: 100%;
    display: -webkit-flex;
    display: flex;
    font-size: 0.65rem;
    color: #000;
}

.date-picker.order-date-picker .date-chose .detail-day .week > div {
    width: 14.2%;
}

.date-picker.order-date-picker .date-chose .detail-day .days {
    -webkit-flex: 1;
    flex: 1;
    display: -webkit-flex;
    display: flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    height: 11rem;
}

.date-picker.order-date-picker .date-chose .detail-day .days > div {
    font-size: 0.65rem;
    width: 14.2%;
    color: #333;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    -webkit-box-pack: center;
    -moz-justify-content: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -moz-align-items: center;
    -webkit-align-items: center;
    align-items: center;

}

.date-picker.order-date-picker .date-chose .detail-day .days > div.active > span {
    display: block;
    width: 1.5rem;
    height: 1.5rem;
    text-align: center;
    line-height: 1.5rem;
    background: #f95959;
    border-radius: 50%;
    color: #fff;
}

.date-picker.order-date-picker .date-chose .detail-day .days > div > span {
    display: block;
    width: 1.5rem;
    height: 1.5rem;
    text-align: center;
    line-height: 1.5rem;
    border-radius: 50%;
}

.date-picker.order-date-picker .option-picker.date-picker {
    height: 19.5rem;
    display: none;
}

.date-picker.order-date-picker .datebtns {
    height: 1.8rem;
    margin: 0.3rem 0 0.9rem;
    line-height: 1.8rem;
    padding: 0 1rem;
}

.date-picker.order-date-picker .date-btn {
    width: 7.25rem;
    height: 1.8rem;
    border-radius: 6px;
    float: left;
    text-align: center;
    color: #fff;
    background: #ff5555;
}

.date-picker.order-date-picker .date-btn-cancel {
    background: #ccc;
}


.fui-topmenu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.fui-header ~ .fui-content .fui-topmenu {
    top: 2.2rem;
}

.fui-tabbar {
    background: white;
    border-bottom: 1px solid #ededed;
}

.fui-tabbar.style1 {
    white-space: nowrap;
    overflow: auto;
    display: -webkit-flex;
    display: flex;
}

.fui-tabbar.style2 {
    -webkit-align-self: center;
    align-self: center;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-lines: single;
    -moz-box-lines: single;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-bottom: .5rem;
}

.fui-tabbar a {
    display: inline-block;
    color: #666;
    font-size: 0.7rem;
    width: 4.25rem;
    height: 1.95rem;
    line-height: 1.95rem;
    border: 0;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    text-align: center;
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: color;
    transition-property: color;
}

.fui-tabbar.style2 a {
    border-radius: 0 0 0 0;
    margin-left: -1px;
    width: 25%;
    color: #666;
}

.fui-tabbar.style1 a {
    width: 4.25rem;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
}

.fui-tabbar a.active {
    -moz-transition-duration: 300ms;
    -webkit-transition-duration: 300ms;
    transition-duration: 300ms;
    -webkit-transition-property: border-color;
    transition-property: border-color;
}

.getmore:active {
    background: #ededed;
}

.fui-modal1.popup-modal {
    display: none;
}