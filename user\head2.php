<?php

if(!empty($conf['staticurl'])){
	$cdnserver = '//'.$conf['staticurl'].'/';
}else{
	$cdnserver = '../';
}
list($background_image, $background_css) = \lib\Template::getBackground('../');

$template_route = \lib\Template::loadRoute();
if($template_route){
	if($template_route['userlogin'] && checkIfActive('login')){
		include($template_route['userlogin']);exit;
	}elseif($template_route['userreg'] && checkIfActive('reg')){
		include($template_route['userreg']);exit;
	}elseif($template_route['userfindpwd'] && checkIfActive('findpwd')){
		include($template_route['userfindpwd']);exit;
	}elseif($template_route['userregsite'] && checkIfActive('regsite')){
		include($template_route['userregsite']);exit;
	}elseif($template_route['userregok'] && checkIfActive('regok')){
		include($template_route['userregok']);exit;
	}
}

@header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title><?php echo $title ?></title>
  <link href="<?php echo $cdnpublic?>twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="<?php echo $cdnpublic?>font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
  <link rel="stylesheet" href="<?php echo $cdnserver?>assets/simple/css/plugins.css">
  <link rel="stylesheet" href="<?php echo $cdnserver?>assets/simple/css/main.css">
  <link rel="stylesheet" href="<?php echo $cdnserver?>assets/css/common.css">
  <script src="<?php echo $cdnpublic?>modernizr/2.8.3/modernizr.min.js"></script>
  <!--[if lt IE 9]>
    <script src="<?php echo $cdnpublic?>html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="<?php echo $cdnpublic?>respond.js/1.4.2/respond.min.js"></script>
  <![endif]-->
<?php echo $background_css?>
</head>
<body>