【2024-11-11】：
1：系统内置CDN资源更新。
2：管理员后台(admin)一键补单优化。
3：用户中心(user) 批量对接优化。(注：订单量超过30条会采用[单个下单]接口批量对接，期间不可关闭网页)
4: 优化了部分细节。

备注：无
#################################################################################

【2024-03-11】：更新内容如下：
1：修复后台商品获取时页面异常。
2：亿乐插件更新。注：因接口调整，成本价格监控强制由[批量]转成[单个]商品同步。
3：[获取订单列表API接口:Path=/api.php?act=orders] 0条订单返回null已修复。
4：后台可自定义设定生成APP接口。
5：文档有几处错误，已修改。
6：后台中心 - 右上角 - 新增查看对接文档的选项。
7：后台分站列表下新增APP列表页面。
8：优化了部分细节。

备注：
1：后台管理 - 对接管理 - (添加/编辑) - 点击刷新对接插件列表(这一点很重要)。
2：JS文件更新，浏览器对本地JS文件有缓存。如新插件不生效，需清空浏览器缓存才能看到最新的效果，请知晓！

#################################################################################

【2024-01-20】：更新内容如下：
1：【晴玖商城】插件更新如下：卡密方式对接不显示卡密信息已修复、订单状态与订单描述重新调整显示。
2：【卡卡云】商城插件已支持多分页查询商品。
3：分站排行奖励制度增加过滤[已退单]订单状态，已退单状态将不会计算在统计之内。

备注：
1：因卡卡云接口调用限制，成本价格监控强制由[批量]转成[单个]商品同步。
2：后台管理 - 对接管理 - (添加/编辑) - 点击刷新对接插件列表(这一点很重要)。
3：JS文件更新，浏览器对本地JS文件有缓存。如新插件不生效，需清空浏览器缓存才能看到最新的效果，请知晓！

